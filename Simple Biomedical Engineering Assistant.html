<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Biomedical Engineering Assistant</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            box-sizing: border-box;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 700px;
            width: 100%;
            text-align: center;
            box-sizing: border-box;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2em;
        }
        p {
            color: #555;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        input[type="text"] {
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1em;
            transition: background-color 0.3s ease;
            width: 100%;
            box-sizing: border-box;
        }
        button:hover {
            background-color: #2980b9;
        }
        #results {
            margin-top: 30px;
            text-align: left;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        #results h2 {
            color: #2c3e50;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 1.5em;
        }
        #results ul {
            list-style-type: disc;
            padding-left: 20px;
            margin-bottom: 15px;
        }
        #results ul li {
            margin-bottom: 8px;
            line-height: 1.5;
            color: #444;
        }
        .error-message {
            color: #e74c3c;
            font-weight: bold;
            margin-top: 20px;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 1.8em;
            }
            p {
                font-size: 0.95em;
            }
            input[type="text"], button {
                font-size: 0.95em;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Biomedical Engineering Assistant</h1>
        <p>This web app provides quick access to relevant technical information for biomedical engineers. Type a device name below to get details on common failure modes, maintenance procedures, and required parts.</p>

        <input type="text" id="deviceInput" placeholder="e.g., ventilator, infusion pump, defibrillator">
        <button onclick="getDeviceInformation()">Get Information</button>

        <div id="results"></div>
    </div>

    <script>
        const devices = {
            "ventilator": {
                failureModes: ["Oxygen sensor failure", "Power supply failure", "Blocked air filter"],
                maintenanceProcedures: ["Calibrate oxygen sensor", "Check power supply voltage", "Replace air filter"],
                partsRequired: ["Oxygen sensor", "Air filter", "Circuit board"]
            },
            "infusion pump": {
                failureModes: ["Occlusion alarm", "Battery drain", "Motor malfunction"],
                maintenanceProcedures: ["Check tubing for kinks", "Calibrate flow rate", "Inspect motor assembly"],
                partsRequired: ["Tubing set", "Battery pack", "Motor assembly"]
            },
            "defibrillator": {
                failureModes: ["Paddles not delivering shock", "Battery low/failure", "ECG signal issues"],
                maintenanceProcedures: ["Check paddle connections", "Battery replacement cycle", "ECG lead integrity test"],
                partsRequired: ["Paddles", "Defibrillator battery", "ECG cable"]
            },
            "patient monitor": {
                failureModes: ["NIBP cuff error", "SpO2 sensor disconnected", "Display not working"],
                maintenanceProcedures: ["Check NIBP hose and cuff", "Verify SpO2 probe connection", "Restart unit and check display cable"],
                partsRequired: ["NIBP cuff", "SpO2 sensor", "LCD display unit"]
            },
            "electrosurgical unit": {
                failureModes: ["No power output", "Return electrode fault", "Foot pedal not responding"],
                maintenanceProcedures: ["Check power cable and circuit breaker", "Inspect return electrode pad and cable", "Test foot pedal continuity"],
                partsRequired: ["Power cord", "Return electrode cable", "Foot pedal switch"]
            },
            "anesthesia machine": {
                failureModes: ["Gas leak", "Vaporizer malfunction", "Ventilator alarm"],
                maintenanceProcedures: ["Leak test system", "Calibrate vaporizer", "Check ventilator settings and hoses"],
                partsRequired: ["Seals and O-rings", "Vaporizer module", "Breathing circuit hoses"]
            },
            "surgical light": {
                failureModes: ["Bulb burnt out", "Dim light output", "Arm drift"],
                maintenanceProcedures: ["Replace bulb/LED module", "Clean light cover", "Adjust arm tension"],
                partsRequired: ["Light bulb/LED module", "Tension springs", "Control board"]
            },
            "ultrasound machine": {
                failureModes: ["Probe not recognized", "Image artifacts", "System freezing"],
                maintenanceProcedures: ["Clean probe connector", "Perform system calibration", "Software update"],
                partsRequired: ["Ultrasound probe", "Coupling gel", "Hard drive/SSD"]
            },
            "hospital bed": {
                failureModes: ["Motor not working", "Side rails stuck", "Brakes failing"],
                maintenanceProcedures: ["Check motor connections", "Lubricate moving parts", "Adjust brake mechanism"],
                partsRequired: ["Bed motor", "Actuator", "Brake assembly"]
            },
            "blood pressure monitor": {
                failureModes: ["Inaccurate readings", "Cuff not inflating", "Display error"],
                maintenanceProcedures: ["Calibrate sensor", "Check cuff and hose for leaks", "Replace battery"],
                partsRequired: ["Pressure sensor", "Cuff and hose", "Battery"]
            }
        };

        function getDeviceInformation() {
            const input = document.getElementById('deviceInput').value.toLowerCase().trim();
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = ''; // Clear previous results

            // Simple "fuzzy" matching: check if input contains any part of a device name
            let foundDevice = null;
            for (const deviceName in devices) {
                // Using includes for simple typo tolerance
                if (deviceName.includes(input) || input.includes(deviceName) || levenshteinDistance(input, deviceName) < 3) {
                    foundDevice = deviceName;
                    break;
                }
            }

            if (foundDevice) {
                const info = devices[foundDevice];

                resultsDiv.innerHTML += `<h2>Common Failure Modes for ${capitalizeFirstLetter(foundDevice)}:</h2><ul>`;
                info.failureModes.forEach(mode => {
                    resultsDiv.innerHTML += `<li>${mode}</li>`;
                });
                resultsDiv.innerHTML += `</ul>`;

                resultsDiv.innerHTML += `<h2>Common Maintenance Procedures for ${capitalizeFirstLetter(foundDevice)}:</h2><ul>`;
                info.maintenanceProcedures.forEach(procedure => {
                    resultsDiv.innerHTML += `<li>${procedure}</li>`;
                });
                resultsDiv.innerHTML += `</ul>`;

                resultsDiv.innerHTML += `<h2>Common Parts Required for Repair for ${capitalizeFirstLetter(foundDevice)}:</h2><ul>`;
                info.partsRequired.forEach(part => {
                    resultsDiv.innerHTML += `<li>${part}</li>`;
                });
                resultsDiv.innerHTML += `</ul>`;

            } else {
                resultsDiv.innerHTML = `<p class="error-message">Device "${input}" not found in our database. Please check the spelling or try another device.</p>`;
            }
        }

        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }

        // Levenshtein distance for more forgiving typo tolerance
        // Source: https://en.wikipedia.org/wiki/Levenshtein_distance#Python
        function levenshteinDistance(a, b) {
            const m = a.length;
            const n = b.length;
            const dp = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));

            for (let i = 0; i <= m; i++) {
                dp[i][0] = i;
            }
            for (let j = 0; j <= n; j++) {
                dp[0][j] = j;
            }

            for (let i = 1; i <= m; i++) {
                for (let j = 1; j <= n; j++) {
                    const cost = (a[i - 1] === b[j - 1]) ? 0 : 1;
                    dp[i][j] = Math.min(
                        dp[i - 1][j] + 1,      // Deletion
                        dp[i][j - 1] + 1,      // Insertion
                        dp[i - 1][j - 1] + cost // Substitution
                    );
                }
            }
            return dp[m][n];
        }
    </script>
</body>
</html>