
import React from 'react';
import { LightBulbIcon } from './icons/ModuleIcons';

interface ModuleDetailPlaceholderProps {
  title: string;
  content: string;
}

const ModuleDetailPlaceholder: React.FC<ModuleDetailPlaceholderProps> = ({ title, content }) => {
  return (
    <div className="p-6 bg-neutral-100 border border-neutral-200 rounded-lg shadow">
      <div className="flex items-center text-primary-600 mb-3">
        <LightBulbIcon className="h-6 w-6 mr-2" />
        <h3 className="text-xl font-semibold">{title}</h3>
      </div>
      <p className="text-neutral-700">
        {content}
      </p>
      <p className="mt-4 text-sm text-neutral-500">
        Further interactive elements and detailed content for this section are planned for future updates.
      </p>
    </div>
  );
};

export default ModuleDetailPlaceholder;
