<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Equipment AI Assistant</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        h1 {
            text-align: center;
            color: #0056b3; /* Hospital blue */
            margin-bottom: 30px;
        }
        .input-section, .knowledge-section, .action-section, .response-section {
            margin-bottom: 25px;
        }
        label, .section-title {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #004085; /* Darker blue */
        }
        #queryInput {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 1rem;
        }
        #knowledgeBaseSelection {
            display: flex;
            flex-wrap: wrap;
            gap: 10px; /* Spacing between items */
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #fdfdfd;
        }
        .knowledge-item {
            display: flex; /* Align checkbox and label */
            align-items: center;
            background-color: #e9f5ff; /* Light blue background for each item */
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #b8dfff;
            font-size: 0.9rem;
            cursor: pointer; /* Make the whole item clickable to toggle checkbox */
        }
        .knowledge-item input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.1); /* Slightly larger checkbox */
            accent-color: #0056b3; /* Checkbox color */
            pointer-events: none; /* Let the div handle click */
        }
        .knowledge-item label {
            margin-bottom: 0; /* Override default label margin */
            font-weight: normal;
            color: #333;
            cursor: pointer;
        }
        #generateButton {
            background-color: #007bff; /* Primary blue */
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            transition: background-color 0.2s ease-in-out;
        }
        #generateButton:hover {
            background-color: #0056b3; /* Darker blue on hover */
        }
        #responseOutput {
            border: 1px solid #e0e0e0;
            padding: 15px;
            min-height: 100px;
            background-color: #fdfdfd;
            border-radius: 4px;
            font-size: 0.95rem;
        }
        #responseOutput p:first-child {
            margin-top: 0;
        }
        #responseOutput p:last-child {
            margin-bottom: 0;
        }
        /* Highlighting styles */
        .highlight-source {
            font-weight: bold;
            color: #0056b3; /* Dark blue for source name */
        }
        .highlight-info {
            background-color: #d1ecf1; /* Light cyan background for info */
            padding: 3px 6px;
            border-radius: 3px;
            display: inline-block; /* Ensures background covers text properly */
            border: 1px solid #bee5eb; /* Light border for info */
        }
        /* Response structure for better readability */
        #responseOutput .response-item-container { /* Each item's contribution */
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #cce5ff; /* Lighter blue border */
            border-radius: 5px;
            background-color: #f0f8ff; /* Very light alice blue */
        }
        #responseOutput .response-item-container:last-child {
            margin-bottom: 0;
        }
        #responseOutput .response-item-container strong { /* "From X Manual:" */
            display: block;
            margin-bottom: 8px;
            font-size: 1.05em;
        }
        #responseOutput ul {
            list-style-type: none; 
            padding-left: 0;
            margin-top: 0;
            margin-bottom: 0; 
        }
        #responseOutput li {
            margin-bottom: 8px;
            padding-left: 15px; 
            position: relative;
        }
        #responseOutput li::before { /* Custom bullet */
            content: "•";
            color: #0056b3;
            font-weight: bold;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0px; /* Adjust vertical alignment of bullet */
        }
        #responseOutput li:last-child {
            margin-bottom: 0;
        }
        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            h1 {
                font-size: 1.5rem;
                margin-bottom: 20px;
            }
            #queryInput, #generateButton {
                padding: 10px;
                font-size: 0.95rem;
            }
            .knowledge-item {
                width: 100%; /* Stack knowledge items on small screens */
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Medical Equipment AI Assistant</h1>

        <div class="input-section">
            <label for="queryInput">Enter your query or question:</label>
            <input type="text" id="queryInput" placeholder="e.g., 'XR-500 error E05', 'calibrate infusion pump IP-200'">
        </div>

        <div class="knowledge-section">
            <p class="section-title">Select Active Knowledge Items:</p>
            <div id="knowledgeBaseSelection">
                <!-- Knowledge items will be populated here by JavaScript -->
            </div>
        </div>

        <div class="action-section">
            <button id="generateButton">Generate Response</button>
        </div>

        <div class="response-section">
            <p class="section-title">AI Response:</p>
            <div id="responseOutput">
                <p><em>The AI's response will appear here. Select knowledge items and enter a query.</em></p>
            </div>
        </div>
    </div>

    <script>
        const knowledgeBase = [
            {
                id: "xr500_manual",
                name: "X-Ray Machine XR-500 Manual",
                type: "Manual",
                keywords: ["x-ray", "xr-500", "manual", "calibration", "error", "e05", "e10", "power supply", "troubleshooting", "maintenance"],
                contentSnippets: [
                    { triggers: ["e05", "error 05"], text: "Error E05 on the XR-500 indicates a problem with the high-voltage power supply. Check connections and voltage readings as per Section 3.2." },
                    { triggers: ["e10", "error 10"], text: "Error E10 on the XR-500 suggests an issue with the detector array. Refer to Section 4.1 for diagnostic steps." },
                    { triggers: ["calibrate", "calibration"], text: "The XR-500 calibration procedure is detailed in Chapter 5. It should be performed monthly." },
                    { triggers: ["power supply"], text: "The XR-500 high-voltage power supply (Part #XR-PSU-001) requires careful handling. Ensure device is powered down before inspection." },
                    { triggers: ["maintenance", "preventive", "pm"], text: "Preventive maintenance for the XR-500 includes daily checks of the cooling system and monthly calibration."}
                ]
            },
            {
                id: "us_p12_log",
                name: "Ultrasound Probe P-12 Repair Log",
                type: "Repair Log",
                keywords: ["ultrasound", "probe", "p-12", "p12", "repair", "log", "artifact", "crystal", "image quality"],
                contentSnippets: [
                    { triggers: ["artifact", "image quality", "distorted image"], text: "The P-12 probe frequently shows image artifacts due to damaged crystal arrays. Last repair on 01/10/2024 replaced a crystal array." },
                    { triggers: ["no image", "blank screen"], text: "If the P-12 probe shows no image, check cable integrity and connection to the main unit. Several instances logged this year related to faulty cables." },
                    { triggers: ["maintenance", "cleaning"], text: "Regular cleaning of the P-12 probe lens and cable inspection is recommended to prevent image degradation."}
                ]
            },
            {
                id: "vent_v70_parts",
                name: "Ventilator V-70 Parts Inventory",
                type: "Parts Inventory",
                keywords: ["ventilator", "v-70", "v70", "parts", "inventory", "filter", "screen", "battery", "sensor"],
                contentSnippets: [
                    { triggers: ["filter"], text: "Air Filter (Part #V70-F01) for V-70: 15 units in stock. HEPA Filter (Part #V70-HF02): 5 units in stock." },
                    { triggers: ["screen", "display"], text: "Display Screen (Part #V70-DS02) for V-70: 2 units in stock. Touch Panel (Part #V70-TP03): On backorder, ETA 2 weeks." },
                    { triggers: ["battery"], text: "Internal Battery (Part #V70-B01) for V-70: 8 units available. Expected lifespan: 2 years." },
                    { triggers: ["oxygen sensor", "o2 sensor"], text: "Oxygen Sensor (Part #V70-OS01) for V-70: 3 units in stock. Calibration required after replacement."}
                ]
            },
            {
                id: "ecg_e300_guide",
                name: "ECG Monitor E-300 Troubleshooting Guide",
                type: "Troubleshooting Guide",
                keywords: ["ecg", "e-300", "e300", "monitor", "troubleshooting", "no signal", "leads", "battery", "artifact", "noise"],
                contentSnippets: [
                    { triggers: ["no signal", "leads off"], text: "For 'No Signal' or 'Leads Off' on E-300: 1. Check lead connections to patient and monitor. 2. Verify lead cable integrity. 3. Ensure correct lead type is selected." },
                    { triggers: ["battery", "power issue"], text: "If E-300 has power issues: 1. Check AC power connection. 2. Test battery charge; replace if below 20% (Part #E300-B01)." },
                    { triggers: ["artifact", "noisy signal", "noise"], text: "Artifacts or noisy ECG signal on E-300: 1. Ensure proper skin preparation. 2. Check for nearby electrical interference. 3. Replace old or damaged leads."}
                ]
            },
            {
                id: "pump_ip200_history",
                name: "Infusion Pump IP-200 Service History",
                type: "Service History",
                keywords: ["infusion pump", "ip-200", "ip200", "service", "history", "maintenance", "calibration", "occlusion", "alarm"],
                contentSnippets: [
                    { triggers: ["maintenance", "pm", "preventive"], text: "IP-200 (SN: IP200-001) last PM: 03/15/2024 (Tubing set replaced, flow rate calibrated). Next PM due: 09/15/2024." },
                    { triggers: ["occlusion alarm", "occlusion"], text: "IP-200 (SN: IP200-002) frequent occlusion alarms on 02/20/2024. Resolved by replacing pressure sensor (Part #IP200-PS01)." },
                    { triggers: ["calibration", "flow rate", "calibrate"], text: "Flow rate calibration for IP-200 pumps is recommended every 6 months or after any major repair."}
                ]
            }
        ];

        function renderKnowledgeItems() {
            const container = document.getElementById('knowledgeBaseSelection');
            container.innerHTML = ''; // Clear existing items if any
            knowledgeBase.forEach(item => {
                const div = document.createElement('div');
                div.classList.add('knowledge-item');
                div.setAttribute('tabindex', '0'); // Make div focusable for accessibility
                div.setAttribute('role', 'checkbox');
                div.setAttribute('aria-checked', 'false');
                div.setAttribute('aria-labelledby', `label-${item.id}`);


                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = item.id;
                checkbox.value = item.id;
                checkbox.name = 'knowledge';

                const label = document.createElement('label');
                label.htmlFor = item.id;
                label.id = `label-${item.id}`;
                label.textContent = item.name;

                div.appendChild(checkbox);
                div.appendChild(label);
                container.appendChild(div);

                // Click on div toggles checkbox
                div.addEventListener('click', () => {
                    checkbox.checked = !checkbox.checked;
                    div.setAttribute('aria-checked', checkbox.checked.toString());
                });
                // Keyboard accessibility for div
                div.addEventListener('keydown', (event) => {
                    if (event.key === ' ' || event.key === 'Enter') {
                        event.preventDefault();
                        checkbox.checked = !checkbox.checked;
                        div.setAttribute('aria-checked', checkbox.checked.toString());
                    }
                });
            });
        }

        function generateAIResponse(query, activeKnowledgeItemIds) {
            const lowerQuery = query.toLowerCase().trim();
            const responseOutput = document.getElementById('responseOutput');

            if (!lowerQuery) {
                responseOutput.innerHTML = "<p>Please enter a query.</p>";
                return;
            }

            const activeItems = knowledgeBase.filter(item => activeKnowledgeItemIds.includes(item.id));

            if (activeItems.length === 0) {
                responseOutput.innerHTML = "<p>Please select at least one knowledge item to get a specific response. The AI's accuracy depends on the information you provide access to.</p>";
                return;
            }

            let htmlResponseChunks = [];
            let foundRelevantInfo = false;

            activeItems.forEach(item => {
                let itemSpecificSnippets = [];
                item.contentSnippets.forEach(snippet => {
                    const triggerFound = snippet.triggers.some(trigger => lowerQuery.includes(trigger.toLowerCase()));
                    
                    if (triggerFound) {
                        itemSpecificSnippets.push(`<span class="highlight-info">${snippet.text}</span>`);
                        foundRelevantInfo = true;
                    }
                });

                if (itemSpecificSnippets.length > 0) {
                    let itemContributionHTML = `<div class="response-item-container">`;
                    itemContributionHTML += `<strong>From <span class="highlight-source">${item.name}</span>:</strong><ul>`;
                    itemSpecificSnippets.forEach(snippetHTML => {
                        itemContributionHTML += `<li>${snippetHTML}</li>`;
                    });
                    itemContributionHTML += `</ul></div>`;
                    htmlResponseChunks.push(itemContributionHTML);
                }
            });

            if (!foundRelevantInfo) {
                let sources = activeItems.map(i => i.name).join(', ');
                if (sources.length > 100) sources = activeItems.length + " selected source(s)";
                // Escape HTML for query display
                const escapedQuery = query.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
                responseOutput.innerHTML = `<p>Based on the selected knowledge item(s) (${sources}), I could not find specific information for your query: "<em>${escapedQuery}</em>". Try rephrasing, checking your spelling, or selecting different/additional knowledge items.</p>`;
            } else {
                responseOutput.innerHTML = htmlResponseChunks.join('');
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            renderKnowledgeItems();

            const generateButton = document.getElementById('generateButton');
            const queryInput = document.getElementById('queryInput');
            
            generateButton.addEventListener('click', () => {
                const query = queryInput.value;
                const selectedCheckboxes = document.querySelectorAll('#knowledgeBaseSelection input[type="checkbox"]:checked');
                const activeKnowledgeItemIds = Array.from(selectedCheckboxes).map(cb => cb.value);
                
                generateAIResponse(query, activeKnowledgeItemIds);
            });
            
            queryInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault(); 
                    generateButton.click(); 
                }
            });
        });
    </script>
</body>
</html>
