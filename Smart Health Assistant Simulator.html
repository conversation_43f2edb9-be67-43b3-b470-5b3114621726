<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Health Assistant Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        h1, h2, h3 {
            color: #005f73; /* A calming, professional blue/teal */
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2em; /* Adjusted for better hierarchy */
        }
        h2 {
            border-bottom: 2px solid #005f73;
            padding-bottom: 10px;
            margin-top: 30px;
            font-size: 1.5em;
        }
        h3 {
            color: #0a9396; /* A slightly lighter shade for subheadings */
            margin-top: 20px;
            font-size: 1.2em;
        }

        section {
            margin-bottom: 30px;
        }

        #symptoms-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .symptom-item {
            display: flex;
            align-items: center;
            background-color: #e9f5f9; /* Light blue background for items */
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #cce0e5;
            transition: background-color 0.2s ease-in-out;
        }
        .symptom-item:hover {
            background-color: #d4edf4;
        }

        .symptom-item input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2); /* Make checkbox slightly larger */
            accent-color: #0077b6; /* Modern way to color checkboxes */
        }
        .symptom-item label {
            cursor: pointer;
            flex-grow: 1;
            font-size: 0.95em;
        }

        #generate-btn {
            display: block;
            width: 100%;
            padding: 12px 20px;
            font-size: 1.1em;
            font-weight: bold;
            color: #fff;
            background-color: #0077b6; /* A strong call-to-action blue */
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }

        #generate-btn:hover {
            background-color: #005f8e; /* Darker shade on hover */
        }

        #assistant-output {
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f9ff; /* Light background for output section */
            border: 1px solid #bde0fe; /* Border to define the section */
            border-radius: 8px;
        }

        #assistant-description-container p,
        #benefits-container ul,
        #limitations-container ul {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            margin-top: 10px;
        }

        ul {
            list-style-type: disc; /* Changed from none for better readability */
            padding-left: 25px; /* Indent list items */
        }
        ul li {
            margin-bottom: 8px;
        }

        #call-to-action p {
            font-weight: bold;
            color: #d9534f; /* A warning-like color */
            text-align: center;
            padding: 15px;
            background-color: #fcf8e3; /* Light yellow for emphasis */
            border: 1px solid #faebcc;
            border-radius: 5px;
            margin-top: 30px;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            h1 {
                font-size: 1.8em;
            }
            h2 {
                font-size: 1.3em;
            }
            h3 {
                font-size: 1.1em;
            }
            #symptoms-list {
                grid-template-columns: 1fr; /* Single column on small screens */
            }
            .symptom-item label {
                font-size: 1em;
            }
            #generate-btn {
                font-size: 1em;
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Smart Health Assistant Simulator</h1>

        <section id="scenario">
            <h2>Scenario</h2>
            <p>You are developing a smart assistant to help people assess their risk of contracting a new virus. The assistant will ask users about their symptoms and then provide a risk assessment and recommendations.</p>
        </section>

        <section id="symptom-selection">
            <h2>1. Select Symptoms for Your Assistant</h2>
            <p>Choose the symptoms your assistant will ask about. You can select as few as one or as many as all of them:</p>
            <div id="symptoms-list">
                <!-- Symptom checkboxes will be populated by JavaScript -->
            </div>
        </section>

        <button id="generate-btn">Generate Assistant</button>

        <section id="assistant-output" style="display: none;">
            <h2>2. Your Simulated Assistant's Profile</h2>
            
            <div id="assistant-description-container">
                <h3>Assistant Capabilities</h3>
                <p id="assistant-description-text"></p>
            </div>

            <div id="benefits-container">
                <h3>Potential Benefits of Such an Assistant</h3>
                <ul id="benefits-list">
                    <!-- Pre-defined benefits will be populated by JavaScript -->
                </ul>
            </div>

            <div id="limitations-container">
                <h3>Potential Limitations of Such an Assistant</h3>
                <ul id="limitations-list">
                    <!-- Pre-defined limitations will be populated by JavaScript -->
                </ul>
            </div>
        </section>

        <section id="call-to-action">
            <p><strong>Consider the benefits and limitations carefully before using this assistant.</strong></p>
        </section>
    </div>

    <script>
        const allSymptoms = [
            { id: 'fever', label: 'Fever' },
            { id: 'cough', label: 'Cough' },
            { id: 'soreThroat', label: 'Sore Throat' },
            { id: 'bodyAches', label: 'Body Aches' },
            { id: 'lossOfTasteSmell', label: 'Loss of Taste/Smell' },
            { id: 'headache', label: 'Headache' },
            { id: 'fatigue', label: 'Fatigue' },
            { id: 'difficultyBreathing', label: 'Difficulty Breathing' },
            { id: 'runnyNose', label: 'Runny Nose' },
            { id: 'nauseaVomiting', label: 'Nausea/Vomiting' },
            { id: 'diarrhea', label: 'Diarrhea' }
        ];

        const predefinedBenefits = [
            "Early detection of potential illness.",
            "Helps individuals make informed decisions about seeking medical care.",
            "Can reduce the spread of the virus by prompting early self-isolation.",
            "May alleviate burden on healthcare systems by filtering non-urgent cases.",
            "Provides accessible health information 24/7.",
            "Can offer personalized recommendations based on symptoms."
        ];

        const predefinedLimitations = [
            "Not a substitute for professional medical diagnosis or advice from a doctor.",
            "Accuracy depends heavily on the quality of the input data and the underlying algorithm.",
            "May cause unnecessary anxiety or provide false reassurance.",
            "Potential for bias in algorithms or data used for training.",
            "May not capture all relevant symptoms, atypical presentations, or individual complexities.",
            "Relies on user self-reporting, which can be subjective or inaccurate.",
            "Data privacy and security concerns regarding sensitive health information.",
            "Cannot perform physical examinations or lab tests.",
            "May not be accessible to all individuals (e.g., those without internet access or digital literacy)."
        ];

        document.addEventListener('DOMContentLoaded', () => {
            const symptomsListDiv = document.getElementById('symptoms-list');
            allSymptoms.forEach(symptom => {
                const itemDiv = document.createElement('div');
                itemDiv.classList.add('symptom-item');

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = symptom.id;
                checkbox.name = 'symptom';
                checkbox.value = symptom.label.toLowerCase(); // Store lowercase label for potential processing
                
                const label = document.createElement('label');
                label.htmlFor = symptom.id;
                label.textContent = symptom.label;
                
                itemDiv.appendChild(checkbox);
                itemDiv.appendChild(label);
                symptomsListDiv.appendChild(itemDiv);
            });

            const benefitsUl = document.getElementById('benefits-list');
            predefinedBenefits.forEach(benefit => {
                const li = document.createElement('li');
                li.textContent = benefit;
                benefitsUl.appendChild(li);
            });

            const limitationsUl = document.getElementById('limitations-list');
            predefinedLimitations.forEach(limitation => {
                const li = document.createElement('li');
                li.textContent = limitation;
                limitationsUl.appendChild(li);
            });

            const generateBtn = document.getElementById('generate-btn');
            generateBtn.addEventListener('click', generateAssistantProfile);
        });

        function generateAssistantProfile() {
            const selectedSymptomsCheckboxes = document.querySelectorAll('#symptoms-list input[name="symptom"]:checked');
            const selectedSymptomsLabels = [];
            selectedSymptomsCheckboxes.forEach(checkbox => {
                // Get the label text associated with the checkbox for natural language
                const labelElement = document.querySelector(`label[for="${checkbox.id}"]`);
                if (labelElement) {
                    selectedSymptomsLabels.push(labelElement.textContent.toLowerCase());
                }
            });

            const assistantDescriptionTextEl = document.getElementById('assistant-description-text');
            const assistantOutputSectionEl = document.getElementById('assistant-output');

            if (selectedSymptomsLabels.length === 0) {
                assistantDescriptionTextEl.textContent = "This assistant is not configured to ask about any specific symptoms. Please select at least one symptom to define its capabilities. Without symptom inputs, it cannot assess risk or provide recommendations.";
            } else {
                let symptomsString = "";
                if (selectedSymptomsLabels.length === 1) {
                    symptomsString = selectedSymptomsLabels[0];
                } else if (selectedSymptomsLabels.length === 2) {
                    symptomsString = selectedSymptomsLabels.join(" and ");
                } else {
                    // Oxford comma for lists of 3 or more
                    symptomsString = selectedSymptomsLabels.slice(0, -1).join(", ") + ", and " + selectedSymptomsLabels.slice(-1);
                }
                assistantDescriptionTextEl.textContent = `This assistant assesses your risk of infection based on ${symptomsString}. It then provides recommendations tailored to the symptoms you report.`;
            }
            
            assistantOutputSectionEl.style.display = 'block';
            // Scroll to the generated content for better UX, especially on mobile
            assistantOutputSectionEl.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    </script>
</body>
</html>
