<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medicine: Doctor vs. AI-Assisted</title>
    <style>
        /* General Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 8px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }

        header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        h2, h3, h4 {
            color: #34495e;
        }

        /* Section Styling */
        section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fdfdfd;
        }

        /* Buttons */
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 18px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 8px 4px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #2980b9;
        }

        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        #mode-selection button {
            margin-right: 10px;
        }

        /* Input Elements */
        textarea {
            width: calc(100% - 22px); /* Account for padding and border */
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: inherit;
            font-size: 1em;
            box-sizing: border-box; /* Important for width calculation */
        }

        /* AI Specific Styles */
        #ai-report-area pre {
            background-color: #e8f4f8;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap; /* Allows text to wrap */
            word-wrap: break-word;
            font-family: "Courier New", Courier, monospace;
            border: 1px dashed #b0c4de;
        }

        .loader {
            border: 5px solid #f3f3f3; /* Light grey */
            border-top: 5px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Feedback Section */
        #feedback-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        #feedback-content h4 {
            margin-top: 0;
        }
        #feedback-content strong {
            color: #2c3e50;
        }

        /* Performance Tracker */
        #performance-tracker p {
            font-size: 1.05em;
            margin: 5px 0;
        }
        #performance-tracker span {
            font-weight: bold;
        }

        /* Learn More Section */
        #learn-more-section ul {
            list-style-type: disc;
            padding-left: 20px;
        }
        #learn-more-section li {
            margin-bottom: 8px;
        }
        #learn-more-section a {
            color: #3498db;
            text-decoration: none;
        }
        #learn-more-section a:hover {
            text-decoration: underline;
        }

        /* Footer */
        footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 0.9em;
            color: #7f8c8d;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            button {
                width: calc(100% - 8px); /* Full width buttons on mobile */
                margin: 8px 0;
            }

            #mode-selection button {
                 margin-right: 0;
            }

            textarea {
                width: 100%; /* Full width */
            }
        }
        
        /* Utility class for hiding elements */
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>AI in Medicine: Augmenting Doctors</h1>
            <p>Explore how AI assists medical professionals in various tasks. This app simulates these interactions for educational purposes.</p>
        </header>

        <section id="task-section">
            <h2>Current Task (<span id="task-counter">1</span>/<span id="total-tasks"></span>): <span id="task-name"></span></h2>
            <p id="task-description"></p>
            <div id="task-image-container" class="hidden">
                <img id="task-image" src="" alt="Medical Image" style="max-width: 100%; height: auto; max-height:300px; display:block; margin: 10px auto; border: 1px solid #ddd;">
            </div>
        </section>

        <section id="mode-selection">
            <p>How would you like to approach this task?</p>
            <button id="select-doctor-mode">Doctor Only</button>
            <button id="select-ai-mode">AI-Assisted Doctor</button>
        </section>

        <section id="doctor-mode-interface" class="hidden">
            <h3>Your Turn: Doctor's Approach</h3>
            <p>Based on the patient's information, enter your diagnosis, treatment plan, or interpretation:</p>
            <textarea id="doctor-input" rows="6" placeholder="e.g., Initial diagnosis: ..., Recommended tests: ..., Treatment plan: ..."></textarea>
            <button id="submit-doctor-response">Submit Response</button>
        </section>

        <section id="ai-mode-interface" class="hidden">
            <h3>Your Turn: AI-Assisted Approach</h3>
            <div id="ai-processing-sim">
                <p>Simulating AI analysis of patient data (e.g., uploading X-ray, processing symptoms)...</p>
                <div class="loader"></div>
            </div>
            <div id="ai-report-area" class="hidden">
                <h4>AI-Generated Insights:</h4>
                <pre id="ai-report-content"></pre>
                <p>Review the AI's findings. Now, provide your final diagnosis, treatment plan, or interpretation, incorporating or validating the AI's suggestions:</p>
                <textarea id="ai-assisted-input" rows="6" placeholder="e.g., Concur with AI on ..., Add consideration for ..., Final plan: ..."></textarea>
                <button id="submit-ai-response">Submit Validated Response</button>
            </div>
        </section>

        <section id="feedback-section" class="hidden">
            <h2>Feedback & Comparison</h2>
            <div id="feedback-content"></div>
            <button id="next-task-button" class="hidden">Next Task</button>
            <button id="restart-button" class="hidden">Restart All Tasks</button>
        </section>

        <section id="performance-tracker">
            <h2>Your Progress</h2>
            <p>Doctor Only "Accuracy": <span id="doctor-accuracy">0%</span> (<span id="doctor-correct">0</span>/<span id="doctor-total">0</span> tasks on track)</p>
            <p>AI-Assisted "Accuracy": <span id="ai-accuracy">0%</span> (<span id="ai-correct">0</span>/<span id="ai-total">0</span> tasks on track)</p>
            <small>Note: "Accuracy" here is a simplified measure based on keyword matching for educational purposes.</small>
        </section>

        <section id="learn-more-section">
            <h2>Learn More About AI in Medicine</h2>
            <ul>
                <li><a href="https://www.nature.com/articles/s41591-020-0789-3" target="_blank" rel="noopener noreferrer">High-performance medicine: the convergence of human and artificial intelligence (Nature Medicine)</a></li>
                <li><a href="https://www.ama-assn.org/practice-management/digital/augmented-intelligence-medicine" target="_blank" rel="noopener noreferrer">Augmented intelligence in medicine (American Medical Association)</a></li>
                <li><a href="https://www.nejm.org/ai-in-medicine" target="_blank" rel="noopener noreferrer">AI in Medicine Collection (New England Journal of Medicine)</a></li>
                <li><a href="https://health.google/caregivers/ai-for-health/" target="_blank" rel="noopener noreferrer">Google AI for Health - For Caregivers</a></li>
                <li><a href="https://www.fda.gov/medical-devices/software-medical-device-samd/artificial-intelligence-and-machine-learning-software-medical-device" target="_blank" rel="noopener noreferrer">Artificial Intelligence and Machine Learning (AI/ML) in Software as a Medical Device (FDA)</a></li>
            </ul>
        </section>

        <footer>
            <p>&copy; 2024 AI in Medicine Educational App. This is a simplified simulation for learning purposes and does not constitute medical advice.</p>
        </footer>
    </div>

    <script>
        // --- DATA: Medical Tasks ---
        const medicalTasks = [
            {
                id: 1,
                name: "Diagnose Chest X-Ray",
                description: "A 55-year-old patient presents with a persistent cough for 3 weeks, fever, and shortness of breath. Review their chest X-Ray (simulated).",
                type: 'image_diagnosis',
                image_url: 'https://via.placeholder.com/400x300.png?text=Chest+X-Ray+Example', // Placeholder image
                ai_simulated_report: "AI Analysis of Chest X-Ray:\n- Opacities consistent with pneumonia identified in the lower left lobe.\n- Possible minor pleural effusion on the left side.\n- No signs of pneumothorax or significant cardiomegaly.\n- Recommendation: Correlate with clinical findings. Consider sputum culture if bacterial pneumonia suspected.",
                correct_keywords: ["pneumonia", "opacity", "effusion", "cough", "fever", "shortness of breath", "left lobe"],
                // Feedback tailored to this task
                comparison_summary: {
                    speed: "AI can analyze images for predefined patterns very quickly, often in seconds.",
                    accuracy_augmentation: "AI can highlight subtle anomalies a busy clinician might miss, acting as a 'second pair of eyes'. However, it can also have false positives/negatives.",
                    human_oversight: "Crucial. The doctor must confirm AI findings, consider the full clinical context (patient history, other symptoms not visible in the image), and make the final diagnosis. AI doesn't understand 'patient context' like a human.",
                    doctor_strengths: "Holistic assessment, integrating diverse information, empathy, communication.",
                    ai_strengths: "Pattern recognition in images, speed, consistency (when well-trained)."
                },
                learning_point: "AI excels at specific, well-defined tasks like image pattern recognition. The doctor integrates this information into a broader clinical picture."
            },
            {
                id: 2,
                name: "Prescribe Medication for Hypertension",
                description: "A 60-year-old patient has consistently high blood pressure readings (average 150/95 mmHg over several visits). They have a history of mild chronic kidney disease (CKD Stage 2) and are not currently on any antihypertensives.",
                type: 'medication_prescription',
                ai_simulated_report: "AI Drug Recommendation & Safety Check (based on guidelines & patient profile):\n- Target BP: <130/80 mmHg (ACC/AHA guidelines).\n- Suggested initial therapy: ACE inhibitor or ARB (renoprotective benefits).\n- Caution: Monitor potassium and renal function closely due to CKD and ACEi/ARB initiation.\n- Alternative if ACEi/ARB contraindicated/not tolerated: Calcium channel blocker (e.g., Amlodipine) or Thiazide diuretic (use with caution in CKD).\n- Drug interaction check: No major interactions with common OTC drugs found for ACEi/ARBs.",
                correct_keywords: ["hypertension", "ace inhibitor", "arb", "lisinopril", "losartan", "amlodipine", "blood pressure", "kidney", "ckd", "monitor function"],
                comparison_summary: {
                    speed: "AI can rapidly cross-reference patient data with extensive drug databases, guidelines, and interaction checkers.",
                    accuracy_augmentation: "AI can help avoid prescription errors by flagging potential contraindications, interactions, or deviations from guidelines, improving patient safety.",
                    human_oversight: "Essential. The doctor considers patient preferences, potential side effects, cost, adherence likelihood, and nuances of the patient's condition that AI might not capture (e.g., patient's social support for managing medication). The final prescription is a clinical judgment.",
                    doctor_strengths: "Understanding patient context, shared decision-making, managing comorbidities complexities.",
                    ai_strengths: "Information retrieval, guideline adherence, interaction checking."
                },
                learning_point: "AI can act as a sophisticated decision support tool for prescribing, enhancing safety and adherence to evidence-based medicine. The doctor remains the ultimate decision-maker."
            },
            {
                id: 3,
                name: "Interpret Blood Test Results",
                description: "A 40-year-old patient's routine blood test panel shows: Glucose (fasting) 115 mg/dL, HbA1c 6.0%, Total Cholesterol 220 mg/dL, LDL 140 mg/dL, HDL 45 mg/dL, Triglycerides 180 mg/dL. Patient is asymptomatic.",
                type: 'lab_interpretation',
                ai_simulated_report: "AI Lab Panel Analysis:\n- Glucose (115 mg/dL): Elevated (Prediabetes range: 100-125 mg/dL).\n- HbA1c (6.0%): Elevated (Prediabetes range: 5.7-6.4%).\n- Total Cholesterol (220 mg/dL): Borderline high (Desirable <200 mg/dL).\n- LDL Cholesterol (140 mg/dL): High (Optimal <100 mg/dL, Near optimal 100-129 mg/dL).\n- HDL Cholesterol (45 mg/dL): Borderline low for men (>40 desirable), acceptable for women (>50 desirable).\n- Triglycerides (180 mg/dL): High (Desirable <150 mg/dL).\n- Summary: Results indicate prediabetes and dyslipidemia. Recommend lifestyle modifications (diet, exercise). Assess cardiovascular risk. Follow-up testing recommended.",
                correct_keywords: ["prediabetes", "glucose", "hba1c", "cholesterol", "ldl", "dyslipidemia", "lifestyle modification", "diet", "exercise", "cardiovascular risk"],
                 comparison_summary: {
                    speed: "AI can instantly flag abnormal values against reference ranges and identify patterns across multiple results.",
                    accuracy_augmentation: "AI ensures no abnormal result is missed and can highlight combined risk factors. It can also track trends over time if historical data is available.",
                    human_oversight: "Critical. The doctor interprets these numbers in the context of the specific patient (family history, lifestyle, other conditions, symptoms or lack thereof), explains the implications, and co-develops a management plan. AI doesn't 'discuss' or 'empathize'.",
                    doctor_strengths: "Contextual interpretation, patient education, motivational interviewing, shared decision making.",
                    ai_strengths: "Data processing, abnormality detection, risk stratification based on algorithms."
                },
                learning_point: "AI provides rapid, data-driven insights from lab results, but the doctor's role in contextual interpretation, communication, and personalized care planning is irreplaceable."
            }
        ];

        // --- DOM Elements ---
        const taskNameEl = document.getElementById('task-name');
        const taskDescriptionEl = document.getElementById('task-description');
        const taskImageContainerEl = document.getElementById('task-image-container');
        const taskImageEl = document.getElementById('task-image');
        const taskCounterEl = document.getElementById('task-counter');
        const totalTasksEl = document.getElementById('total-tasks');

        const modeSelectionSection = document.getElementById('mode-selection');
        const selectDoctorModeBtn = document.getElementById('select-doctor-mode');
        const selectAIModeBtn = document.getElementById('select-ai-mode');

        const doctorModeInterface = document.getElementById('doctor-mode-interface');
        const doctorInputEl = document.getElementById('doctor-input');
        const submitDoctorResponseBtn = document.getElementById('submit-doctor-response');

        const aiModeInterface = document.getElementById('ai-mode-interface');
        const aiProcessingSimEl = document.getElementById('ai-processing-sim');
        const aiReportAreaEl = document.getElementById('ai-report-area');
        const aiReportContentEl = document.getElementById('ai-report-content');
        const aiAssistedInputEl = document.getElementById('ai-assisted-input');
        const submitAIResponseBtn = document.getElementById('submit-ai-response');

        const feedbackSection = document.getElementById('feedback-section');
        const feedbackContentEl = document.getElementById('feedback-content');
        const nextTaskBtn = document.getElementById('next-task-button');
        const restartBtn = document.getElementById('restart-button');

        const doctorAccuracyEl = document.getElementById('doctor-accuracy');
        const doctorCorrectEl = document.getElementById('doctor-correct');
        const doctorTotalEl = document.getElementById('doctor-total');
        const aiAccuracyEl = document.getElementById('ai-accuracy');
        const aiCorrectEl = document.getElementById('ai-correct');
        const aiTotalEl = document.getElementById('ai-total');

        // --- App State ---
        let currentTaskIndex = 0;
        let userScores = {
            doctor: { correct: 0, total: 0 },
            ai_assisted: { correct: 0, total: 0 }
        };
        let currentMode = null; // 'doctor' or 'ai_assisted'

        // --- Functions ---

        /**
         * Loads and displays the current task.
         * @param {number} taskIndex - The index of the task in medicalTasks array.
         */
        function loadTask(taskIndex) {
            if (taskIndex >= medicalTasks.length) {
                displayEndOfApp();
                return;
            }
            currentMode = null;
            const task = medicalTasks[taskIndex];

            taskNameEl.textContent = task.name;
            taskDescriptionEl.innerHTML = task.description; // Use innerHTML if description contains HTML
            taskCounterEl.textContent = taskIndex + 1;

            // Handle image display for image_diagnosis tasks
            if (task.type === 'image_diagnosis' && task.image_url) {
                taskImageEl.src = task.image_url;
                taskImageContainerEl.classList.remove('hidden');
            } else {
                taskImageContainerEl.classList.add('hidden');
            }

            // Reset and show/hide UI sections
            modeSelectionSection.classList.remove('hidden');
            doctorModeInterface.classList.add('hidden');
            aiModeInterface.classList.add('hidden');
            feedbackSection.classList.add('hidden');
            nextTaskBtn.classList.add('hidden');
            restartBtn.classList.add('hidden');
            doctorInputEl.value = '';
            aiAssistedInputEl.value = '';
            aiReportAreaEl.classList.add('hidden'); // Ensure AI report is hidden initially for AI mode
            aiProcessingSimEl.classList.remove('hidden'); // Ensure processing sim is shown initially for AI mode
        }

        /**
         * Handles the selection of 'Doctor Only' mode.
         */
        function handleSelectDoctorMode() {
            currentMode = 'doctor';
            modeSelectionSection.classList.add('hidden');
            doctorModeInterface.classList.remove('hidden');
            aiModeInterface.classList.add('hidden');
        }

        /**
         * Handles the selection of 'AI-Assisted Doctor' mode.
         */
        function handleSelectAIMode() {
            currentMode = 'ai_assisted';
            modeSelectionSection.classList.add('hidden');
            doctorModeInterface.classList.add('hidden');
            aiModeInterface.classList.remove('hidden');
            aiReportAreaEl.classList.add('hidden'); // Hide report first
            aiProcessingSimEl.classList.remove('hidden'); // Show loader

            // Simulate AI processing
            setTimeout(() => {
                const task = medicalTasks[currentTaskIndex];
                aiReportContentEl.textContent = task.ai_simulated_report;
                aiProcessingSimEl.classList.add('hidden');
                aiReportAreaEl.classList.remove('hidden');
            }, 2000); // Simulate 2 seconds of processing
        }

        /**
         * Evaluates the user's response based on keywords.
         * @param {string} userInput - The text entered by the user.
         * @param {string} mode - 'doctor' or 'ai_assisted'.
         */
        function evaluateResponse(userInput, mode) {
            const task = medicalTasks[currentTaskIndex];
            let score = 0;
            const keywords = task.correct_keywords;
            const lowerUserInput = userInput.toLowerCase();

            keywords.forEach(keyword => {
                if (lowerUserInput.includes(keyword.toLowerCase())) {
                    score++;
                }
            });

            // Consider "correct" if at least 50% of keywords (or min 2 keywords) are present
            const isCorrect = (keywords.length > 0) && (score >= Math.max(2, Math.floor(keywords.length * 0.4)));

            userScores[mode].total++;
            if (isCorrect) {
                userScores[mode].correct++;
            }

            updatePerformanceTracker();
            displayFeedback(userInput, mode, isCorrect, task);

            // Hide input sections, show feedback
            doctorModeInterface.classList.add('hidden');
            aiModeInterface.classList.add('hidden');
            feedbackSection.classList.remove('hidden');
            if (currentTaskIndex < medicalTasks.length - 1) {
                nextTaskBtn.classList.remove('hidden');
            } else {
                nextTaskBtn.classList.add('hidden'); // No next task
                restartBtn.classList.remove('hidden'); // Show restart at the end
                displayEndOfAppSummary(); // Show final summary message in feedback
            }
        }

        /**
         * Displays feedback to the user after they submit a response.
         * @param {string} userInput - The user's submitted text.
         * @param {string} mode - The mode ('doctor' or 'ai_assisted') the user chose.
         * @param {boolean} isCorrect - Whether the user's response was deemed "on track".
         * @param {object} task - The current task object.
         */
        function displayFeedback(userInput, mode, isCorrect, task) {
            let html = `<h4>Your Response (${mode === 'doctor' ? 'Doctor Only' : 'AI-Assisted'}):</h4>`;
            html += `<pre style="background-color:#fff; border:1px solid #ddd; padding:8px; white-space:pre-wrap; word-wrap:break-word;">${userInput || "(No input provided)"}</pre>`;
            
            if (isCorrect) {
                html += `<p><strong>Assessment:</strong> Your response seems to be on the right track, covering several key aspects!</p>`;
            } else {
                html += `<p><strong>Assessment:</strong> Your response is a good start. Consider if all key aspects of the AI report (if used) or scenario were addressed. (For this simulation, we check for keywords like: ${task.correct_keywords.slice(0,3).join(', ')}, etc.)</p>`;
            }

            html += `<h4>Understanding the Approaches:</h4>`;

            if (mode === 'doctor') {
                html += `<p><strong>Doctor-Only Approach:</strong></p>
                         <ul>
                            <li><strong>Strengths:</strong> Relies on comprehensive clinical training, experience, intuition, and direct patient interaction (though simulated here). Allows for holistic assessment.</li>
                            <li><strong>Potential Challenges:</strong> Can be time-consuming for complex data analysis, human memory limitations for vast amounts of information (e.g., all drug interactions), potential for cognitive biases.</li>
                         </ul>`;
                html += `<p><strong>How AI Could Augment:</strong> In this scenario, an AI could have rapidly processed data (like the ${task.type === 'image_diagnosis' ? 'X-ray' : task.type === 'medication_prescription' ? 'drug database' : 'lab results'}), highlighted key findings, or checked against guidelines, freeing up the doctor to focus on complex reasoning and patient communication.</p>`;
            } else { // AI-Assisted
                html += `<p><strong>AI-Assisted Approach:</strong></p>
                         <ul>
                            <li><strong>Strengths:</strong> AI provides rapid analysis, pattern recognition, and data-driven insights (like the simulated report you saw). This can improve efficiency and highlight areas of focus.</li>
                            <li><strong>Human Role is Key:</strong> Your validation and refinement of the AI's output are crucial. AI can make errors, lack contextual understanding, or have biases from its training data. The doctor's expertise is essential to interpret AI suggestions critically, integrate them with other knowledge, and make the final, patient-centered decision.</li>
                         </ul>`;
                html += `<p><strong>The Importance of Oversight:</strong> Relying solely on AI without critical human oversight can lead to errors. The doctor's judgment is paramount.</p>`;
            }

            html += `<h4>Key Considerations for this Task (${task.name}):</h4>`;
            html += `<p><strong>Speed:</strong> ${task.comparison_summary.speed}</p>`;
            html += `<p><strong>Accuracy & Augmentation:</strong> ${task.comparison_summary.accuracy_augmentation}</p>`;
            html += `<p><strong>Human Oversight & Doctor's Role:</strong> ${task.comparison_summary.human_oversight}</p>`;
            html += `<p><strong>Doctor's Core Strengths:</strong> ${task.comparison_summary.doctor_strengths}</p>`;
            html += `<p><strong>AI's Core Strengths:</strong> ${task.comparison_summary.ai_strengths}</p>`;
            
            html += `<hr><p><strong>Main Learning Point:</strong> ${task.learning_point} AI is a powerful tool that can <em>augment</em> a doctor's abilities, not replace them. Ethical use, validation, and maintaining the doctor-patient relationship are vital.</p>`;

            feedbackContentEl.innerHTML = html;
        }


        /**
         * Updates the performance tracker display.
         */
        function updatePerformanceTracker() {
            doctorAccuracyEl.textContent = userScores.doctor.total > 0 ? `${Math.round((userScores.doctor.correct / userScores.doctor.total) * 100)}%` : '0%';
            doctorCorrectEl.textContent = userScores.doctor.correct;
            doctorTotalEl.textContent = userScores.doctor.total;

            aiAccuracyEl.textContent = userScores.ai_assisted.total > 0 ? `${Math.round((userScores.ai_assisted.correct / userScores.ai_assisted.total) * 100)}%` : '0%';
            aiCorrectEl.textContent = userScores.ai_assisted.correct;
            aiTotalEl.textContent = userScores.ai_assisted.total;
        }

        /**
         * Moves to the next task or ends the app.
         */
        function handleNextTask() {
            currentTaskIndex++;
            loadTask(currentTaskIndex);
        }

        /**
         * Displays a message when all tasks are completed.
         */
        function displayEndOfApp() {
            taskNameEl.textContent = "All Tasks Completed!";
            taskDescriptionEl.innerHTML = "<p>You have completed all the simulated medical tasks. We hope this interactive experience provided insights into how AI can assist doctors.</p><p>Review your overall performance below and check out the 'Learn More' section for further reading.</p>";
            taskImageContainerEl.classList.add('hidden');
            modeSelectionSection.classList.add('hidden');
            doctorModeInterface.classList.add('hidden');
            aiModeInterface.classList.add('hidden');
            feedbackSection.classList.remove('hidden'); // Keep feedback section visible for final summary
            nextTaskBtn.classList.add('hidden');
            restartBtn.classList.remove('hidden');
        }
        
        /**
         * Displays a summary message in the feedback area at the end of all tasks.
         */
        function displayEndOfAppSummary() {
            let summaryHtml = `<h2>Congratulations!</h2>
            <p>You've completed all tasks in this simulation. This exercise aimed to highlight:</p>
            <ul>
                <li>How AI can process information quickly and identify patterns.</li>
                <li>The importance of the doctor's expertise in validating AI outputs, considering broader context, and making final decisions.</li>
                <li>That AI is a tool to augment, not replace, skilled medical professionals.</li>
            </ul>
            <p>Your performance scores reflect engagement with the scenarios based on simplified keyword matching. The key takeaway is understanding the collaborative potential of human and artificial intelligence in medicine.</p>
            <p>Consider exploring the 'Learn More' resources below to deepen your understanding.</p>`;
            feedbackContentEl.innerHTML = summaryHtml;
        }


        /**
         * Resets the application to its initial state.
         */
        function restartApp() {
            currentTaskIndex = 0;
            userScores = {
                doctor: { correct: 0, total: 0 },
                ai_assisted: { correct: 0, total: 0 }
            };
            updatePerformanceTracker();
            loadTask(currentTaskIndex);
            restartBtn.classList.add('hidden');
            feedbackSection.classList.add('hidden'); // Hide feedback section on restart
        }

        /**
         * Initializes the application.
         */
        function initApp() {
            totalTasksEl.textContent = medicalTasks.length;

            // Event Listeners
            selectDoctorModeBtn.addEventListener('click', handleSelectDoctorMode);
            selectAIModeBtn.addEventListener('click', handleSelectAIMode);
            submitDoctorResponseBtn.addEventListener('click', () => evaluateResponse(doctorInputEl.value, 'doctor'));
            submitAIResponseBtn.addEventListener('click', () => evaluateResponse(aiAssistedInputEl.value, 'ai_assisted'));
            nextTaskBtn.addEventListener('click', handleNextTask);
            restartBtn.addEventListener('click', restartApp);

            // Load the first task
            loadTask(currentTaskIndex);
            updatePerformanceTracker(); // Initial display
        }

        // Start the app when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>
