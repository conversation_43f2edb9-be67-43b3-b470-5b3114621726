
import React from 'react';

export enum LearningTrack {
  CLINICAL = 'Clinical Professionals',
  ENGINEERING = 'Engineering Professionals',
  BOTH = 'All Professionals'
}

export interface Module {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  learningTrack: LearningTrack;
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  tags: string[];
  interactiveComponentKey?: string; // Key to identify a specific interactive component
  htmlFileName?: string; // For HTML-based modules
  content?: {
    overview: string;
    sections?: Array<{ title: string; content: string; subSections?: Array<{ title: string; content: string; }> }>;
    caseStudies?: Array<{ title: string; description: string; outcome: string; }>;
    assessment?: { type: string; questions: Array<{ question: string; options: string[]; answer: string; }> };
  }
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}
