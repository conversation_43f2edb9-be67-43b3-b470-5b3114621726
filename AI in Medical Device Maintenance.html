<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medical Device Maintenance</title>
    <style>
        :root {
            --primary-bg: #f4f7f6;
            --secondary-bg: #ffffff;
            --text-color: #333333;
            --header-footer-bg: #004d40; /* Dark Teal */
            --header-footer-text: #ffffff;
            --component-bg: #e0f2f1; /* Light Teal */
            --component-border: #00796b; /* Teal */
            --component-selected-bg: #b2dfdb; /* Medium Teal */
            --component-selected-border: #004d40; /* Dark Teal */
            --info-panel-bg: #e8f5e9; /* Light Green */
            --malfunction-button-bg: #007bff; /* Blue */
            --malfunction-button-text: #ffffff;
            --manual-button-bg: #ffc107; /* Amber */
            --manual-button-text: #333333;
            --ai-button-bg: #17a2b8; /* Info Blue */
            --ai-button-text: #ffffff;
            --ai-log-bg: #263238; /* Dark Grey Blue */
            --ai-log-text: #c5cae9; /* Light Indigo */
            --box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            --border-radius: 8px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            background-color: var(--primary-bg);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header, footer {
            background-color: var(--header-footer-bg);
            color: var(--header-footer-text);
            padding: 1em;
            text-align: center;
        }
        
        header h1, footer p {
            margin: 0;
        }

        main {
            flex: 1;
            padding: 1em;
            display: flex;
            justify-content: center;
        }

        #app-container {
            display: flex;
            gap: 1.5em;
            width: 100%;
            max-width: 1200px;
        }

        #device-selection-area {
            flex: 1;
            background-color: var(--secondary-bg);
            padding: 1.5em;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            min-width: 300px; /* Ensure it doesn't get too squished */
        }

        #device-selection-area h2 {
            margin-top: 0;
            color: var(--component-border);
            text-align: center;
            margin-bottom: 1.5em;
        }

        #ekg-diagram {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: var(--border-radius);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            height: auto; /* Adjusted for responsiveness */
            min-height: 300px; /* Minimum height */
            justify-content: space-around;
        }

        .ekg-main-unit {
            background-color: #d3d3d3;
            padding: 15px;
            border-radius: 6px;
            width: 90%;
            max-width: 250px; /* Max width for the main unit */
            display: flex;
            flex-direction: column;
            gap: 10px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
        }

        .component-area {
            background-color: var(--component-bg);
            border: 2px solid var(--component-border);
            padding: 10px;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s;
            font-size: 0.9em;
        }

        .component-area:hover {
            background-color: var(--component-selected-bg);
        }

        .component-area.selected-component {
            background-color: var(--component-selected-bg);
            border-color: var(--component-selected-border);
            font-weight: bold;
        }

        #ekg-screen-area {
            height: 80px; /* Relative height */
            background-color: #c0c0ff; /* Light blue for screen */
        }

        .ekg-controls-printer {
            display: flex;
            gap: 10px;
        }

        #ekg-printer-area, #ekg-power-area {
            flex: 1;
            height: 50px; /* Relative height */
        }
        #ekg-printer-area { background-color: #e0e0e0; } /* Light grey for printer */
        #ekg-power-area { background-color: #c8e6c9; } /* Light green for power */


        #ekg-leads-area {
            width: 70%;
            max-width: 200px; /* Max width for leads connector */
            padding: 10px;
            background-color: #ffe0b2; /* Light orange for leads */
            position: relative; /* For lead wires if absolutely positioned */
        }
        
        .leads-wires-container { /* Optional: if you want to visually represent wires */
            display: flex;
            justify-content: space-around;
            margin-top: 5px;
        }
        .lead-wire {
            width: 4px;
            height: 20px;
            background-color: #757575; /* Grey for wires */
            border-radius: 2px;
            display: inline-block; /* if not using flex */
            margin: 0 2px;
        }


        #info-area {
            flex: 2;
            background-color: var(--secondary-bg);
            padding: 1.5em;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow-y: auto;
        }

        .info-panel h3, .info-panel h4 {
            color: var(--component-border);
            margin-top: 0;
            margin-bottom: 0.5em;
        }
        .info-panel p {
            margin-bottom: 1em;
        }

        #malfunction-list {
            list-style: none;
            padding: 0;
        }

        #malfunction-list li {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            padding: 0.8em;
            margin-bottom: 0.5em;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap; /* Allow buttons to wrap on small screens */
        }
        
        #malfunction-list li .malfunction-name-text {
            flex-grow: 1;
            margin-right: 10px; /* Space before buttons */
        }

        #malfunction-list button {
            padding: 0.5em 0.8em;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
            margin-left: 5px; /* Space between buttons */
            margin-top: 5px; /* Space if buttons wrap */
        }

        .manual-btn {
            background-color: var(--manual-button-bg);
            color: var(--manual-button-text);
        }
        .manual-btn:hover { background-color: #ffb300; }

        .ai-btn {
            background-color: var(--ai-button-bg);
            color: var(--ai-button-text);
        }
        .ai-btn:hover { background-color: #138496; }


        #troubleshooting-area {
            margin-top: 1em;
        }
        #troubleshooting-area h3 {
            font-size: 1.4em;
            margin-bottom: 0.8em;
        }
        #troubleshooting-area h4 {
            font-size: 1.1em;
            margin-bottom: 0.3em;
        }

        #manual-troubleshooting p, #ai-troubleshooting #ai-recommendation {
            background-color: var(--info-panel-bg);
            padding: 0.8em;
            border-radius: 4px;
            border-left: 4px solid var(--component-border);
        }
         #manual-troubleshooting p { border-left-color: var(--manual-button-bg); }
         #ai-troubleshooting #ai-recommendation { border-left-color: var(--ai-button-bg); }


        #ai-log {
            background-color: var(--ai-log-bg);
            color: var(--ai-log-text);
            padding: 0.8em;
            border-radius: 4px;
            margin-bottom: 1em;
            max-height: 200px;
            overflow-y: auto;
            font-family: "Courier New", Courier, monospace;
            font-size: 0.9em;
        }
        #ai-log p {
            margin: 0.3em 0;
            padding: 0;
            border: none; /* Override general p styling within ai-log */
            background-color: transparent;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            #app-container {
                flex-direction: column;
            }

            #device-selection-area, #info-area {
                flex: none; /* Reset flex grow/shrink */
                width: auto; /* Allow full width */
            }
            
            #ekg-diagram {
                min-height: 250px; /* Adjust for smaller screens */
            }
            .ekg-main-unit {
                max-width: 200px;
            }
            #ekg-screen-area { height: 60px; }
            #ekg-printer-area, #ekg-power-area { height: 40px; }

            #malfunction-list li {
                flex-direction: column;
                align-items: flex-start;
            }
            #malfunction-list li .malfunction-name-text {
                margin-bottom: 8px;
            }
            #malfunction-list li div { /* Container for buttons */
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }
             #malfunction-list li button {
                margin-top: 0; /* Reset from general rule if wrapped */
             }
        }
    </style>
</head>
<body>
    <header>
        <h1>AI in Medical Device Maintenance</h1>
    </header>

    <main>
        <div id="app-container">
            <div id="device-selection-area">
                <h2>EKG Machine Components</h2>
                <div id="ekg-diagram">
                    <div class="ekg-main-unit">
                        <div id="ekg-screen-area" class="component-area" data-component="screen">Display Screen</div>
                        <div class="ekg-controls-printer">
                            <div id="ekg-printer-area" class="component-area" data-component="printer">Printer</div>
                            <div id="ekg-power-area" class="component-area" data-component="power">Power & Controls</div>
                        </div>
                    </div>
                    <div id="ekg-leads-area" class="component-area" data-component="leads">
                        Leads Connector
                        <div class="leads-wires-container">
                            <div class="lead-wire"></div>
                            <div class="lead-wire"></div>
                            <div class="lead-wire"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="info-area">
                <div id="initial-message" class="info-panel">
                    <p>Select a component on the EKG diagram to learn about its function, potential malfunctions, and how AI can assist in troubleshooting.</p>
                </div>

                <div id="component-info" class="info-panel" style="display:none;">
                    <h3 id="component-name"></h3>
                    <p id="component-description"></p>
                    <h4>Potential Malfunctions:</h4>
                    <ul id="malfunction-list"></ul>
                </div>

                <div id="troubleshooting-area" class="info-panel" style="display:none;">
                    <h3 id="malfunction-title"></h3>
                    <div id="manual-troubleshooting" style="display:none;">
                        <h4>Manual Troubleshooting Steps:</h4>
                        <p id="manual-steps"></p>
                    </div>
                    <div id="ai-troubleshooting" style="display:none;">
                        <h4>AI-Assisted Diagnosis:</h4>
                        <div id="ai-log"></div>
                        <p id="ai-recommendation"></p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>This app demonstrates the increasing use of AI in diagnosing and maintaining medical devices.</p>
    </footer>

    <script>
        const medicalDeviceData = {
            ekg: {
                name: "Electrocardiogram (EKG) Machine",
                components: {
                    screen: {
                        name: "Display Screen",
                        description: "Shows the EKG waveform, patient data, and system status messages.",
                        malfunctions: [
                            {
                                name: "Blank Screen",
                                manualSteps: "1. Check power connection to the EKG and screen. 2. Verify display cable is securely connected at both ends. 3. Attempt to restart the EKG machine. 4. If available, test with an external monitor. 5. If unresolved, screen unit or internal graphics controller may be faulty; requires technician.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: Querying power management unit...",
                                        "AI: Power status: Nominal.",
                                        "AI: Checking display driver integrity...",
                                        "AI: Display driver: Responding.",
                                        "AI: Pinging display controller self-test...",
                                        "AI: Self-test: No response from primary display controller.",
                                        "AI: Analyzing error logs for related faults...",
                                        "AI: Log entry found: 'ERR_DISPLAY_CTRL_TIMEOUT'."
                                    ],
                                    recommendation: "Suspected primary display controller failure. Check internal connections if qualified. Otherwise, schedule service for display assembly or mainboard diagnostics."
                                }
                            },
                            {
                                name: "Flickering Display",
                                manualSteps: "1. Check display cable for loose connections or damage. 2. Ensure cable is not near sources of electromagnetic interference. 3. Test with a different, known-good display cable. 4. Adjust display refresh rate settings if accessible. 5. Internal component issue (e.g., inverter, backlight) may require service.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: Initiating display signal stability test...",
                                        "AI: Signal analysis: Intermittent voltage drops detected on data lines.",
                                        "AI: Cross-referencing with historical EMI patterns... Low probability of EMI.",
                                        "AI: Checking cable connection integrity sensors (if available)... Nominal.",
                                        "AI: Pattern suggests potential loose internal FPC connector or early-stage GPU/display panel fault."
                                    ],
                                    recommendation: "Inspect internal display flexible printed circuit (FPC) cable. If secure, monitor closely. If flickering worsens or persists, further diagnostics on GPU or display panel needed."
                                }
                            }
                        ]
                    },
                    leads: {
                        name: "Leads & Electrodes",
                        description: "These cables and sensors detect the heart's electrical activity via electrodes placed on the patient's skin.",
                        malfunctions: [
                            {
                                name: "Noisy Signal / Artifacts",
                                manualSteps: "1. Ensure proper patient preparation (clean, dry skin). 2. Check electrode-skin contact; re-apply conductive gel if needed. 3. Confirm patient is still and relaxed, minimize muscle movement. 4. Inspect lead wires for damage, kinks, or breaks. 5. Check for nearby electronic devices causing interference (e.g., mobile phones, other medical equipment). 6. Replace leads if they are old or suspected faulty.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: Analyzing waveform frequency spectrum...",
                                        "AI: High-frequency noise detected, consistent with muscle artifact or 60Hz/50Hz interference.",
                                        "AI: Querying patient movement sensor (simulated)... Status: Minimal movement.",
                                        "AI: Checking lead impedance values...",
                                        "AI: Lead V3 shows impedance > 50 kOhms (threshold: 10 kOhms).",
                                        "AI: Lead aVL shows intermittent high impedance."
                                    ],
                                    recommendation: "High impedance on Lead V3 indicates poor electrode contact or faulty lead. Check/replace electrode and re-gel for V3. Monitor Lead aVL for consistent connection. Advise minimizing nearby electrical devices."
                                }
                            },
                            {
                                name: "Flat Line (Lead Off / Asystole Concern)",
                                manualSteps: "1. URGENT: Immediately assess patient's clinical condition (responsiveness, pulse, breathing)! 2. Verify all leads are securely connected to patient and machine. 3. Check lead selector settings on the EKG. 4. Ensure correct lead placement. 5. If 'lead off' message present, address specific lead. 6. If true asystole suspected, initiate ACLS protocol.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: CRITICAL ALERT! Flat line detected on monitored leads.",
                                        "AI: Querying all lead connection statuses...",
                                        "AI: Leads I, II, III, aVR, aVL, aVF report 'Disconnected' or 'High Impedance'.",
                                        "AI: Chest leads (V1-V6) status: Mixed, some reporting 'Disconnected'.",
                                        "AI: Cross-referencing with patient physiological sensors (e.g., SpO2 - simulated)... SpO2: 98%, HR (from SpO2): 75 bpm.",
                                        "AI: Data inconsistent with true cardiac asystole."
                                    ],
                                    recommendation: "URGENT - This is highly indicative of a 'Multiple Leads Off' technical fault, not true asystole, given conflicting SpO2/HR data. Immediately verify all limb and chest lead connections to the patient and the EKG machine. Check lead cable integrity."
                                }
                            }
                        ]
                    },
                    printer: {
                        name: "Thermal Printer",
                        description: "Prints the EKG waveform and diagnostic information on thermal paper.",
                        malfunctions: [
                            {
                                name: "Paper Jam",
                                manualSteps: "1. Turn off printer power or open printer access door. 2. Gently locate and remove any jammed paper, pulling in the direction of paper travel if possible. 3. Check paper path for small torn pieces of paper or foreign objects. 4. Ensure paper roll is loaded correctly and paper guides are set. 5. Close printer door/cover and attempt a test print.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: Detecting printer motor stall via current sensor...",
                                        "AI: Paper feed sensor reports 'Paper Obstructed'.",
                                        "AI: Roller engagement sensor indicates 'Not Engaged' or 'Slipping'.",
                                        "AI: Analyzing sensor data sequence... Pattern suggests paper caught near primary feed roller assembly."
                                    ],
                                    recommendation: "Power off printer. Open printer cover and carefully inspect the primary feed roller area for jammed paper. Remove obstruction and ensure paper path is clear. Verify paper loading."
                                }
                            },
                            {
                                name: "Faded or Blank Print",
                                manualSteps: "1. Confirm correct type of thermal paper is being used. 2. Check if paper roll is loaded correctly (thermal side towards print head). 3. Clean the thermal print head gently with an appropriate cleaning pen or isopropyl alcohol on a lint-free cloth (allow to dry). 4. Try a new roll of thermal paper. 5. If adjustable, check print density settings. 6. If issue persists, print head may be failing.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: Analyzing print quality sensor (simulated)... Low contrast detected.",
                                        "AI: Querying printer settings... Print density: Normal.",
                                        "AI: Checking thermal print head temperature sensor... Temperature within optimal range during print cycle.",
                                        "AI: Cross-referencing with maintenance log... Last print head cleaning: Overdue (6+ months).",
                                        "AI: Paper type sensor (simulated)... Paper type appears compatible."
                                    ],
                                    recommendation: "Primary recommendation: Clean the thermal print head. If issue persists after cleaning, try a new, quality roll of thermal paper. If still faded, the thermal print head itself may require replacement."
                                }
                            }
                        ]
                    },
                    power: {
                        name: "Power System & Battery",
                        description: "Manages AC mains power input and provides backup battery power.",
                        malfunctions: [
                            {
                                name: "Device Not Powering On (AC or Battery)",
                                manualSteps: "1. Check AC power cord is securely connected to device and wall outlet. 2. Verify wall outlet has power using another device. 3. Check device's main power switch/button. 4. If device has a removable battery, try removing and reinserting it, or try powering on with AC only (if battery is suspect). 5. Check for any visible fuses and inspect if blown (if user-serviceable). 6. Internal power supply unit (PSU) or mainboard fault may require technician.",
                                aiDiagnosis: {
                                    log: [
                                        "AI: Querying main AC power input status... No AC power detected at PSU input.",
                                        "AI: Querying battery charge level... Battery voltage: Critically low (3%).",
                                        "AI: Attempting to initiate charging sequence... Charging circuit not responding.",
                                        "AI: Analyzing power management IC (PMIC) logs... Log indicates 'No Input Power' and 'Battery Undervoltage Lockout'.",
                                        "AI: PMIC self-test: Fails to initialize."
                                    ],
                                    recommendation: "Verify AC power source and cord integrity first. If AC confirmed available, suspect fault in AC inlet, internal PSU, or PMIC. Device may not power on due to critically low battery and inability to charge. Service required for internal power system diagnostics."
                                }
                            }
                        ]
                    }
                }
            }
        };

        document.addEventListener('DOMContentLoaded', () => {
            const componentAreas = document.querySelectorAll('.component-area');
            const initialMessageEl = document.getElementById('initial-message');
            const componentInfoEl = document.getElementById('component-info');
            const componentNameEl = document.getElementById('component-name');
            const componentDescriptionEl = document.getElementById('component-description');
            const malfunctionListEl = document.getElementById('malfunction-list');

            const troubleshootingAreaEl = document.getElementById('troubleshooting-area');
            const malfunctionTitleEl = document.getElementById('malfunction-title');
            const manualTroubleshootingEl = document.getElementById('manual-troubleshooting');
            const manualStepsEl = document.getElementById('manual-steps');
            const aiTroubleshootingEl = document.getElementById('ai-troubleshooting');
            const aiLogEl = document.getElementById('ai-log');
            const aiRecommendationEl = document.getElementById('ai-recommendation');

            let currentAiAnimationTimeout = null;

            function clearSelections() {
                componentAreas.forEach(area => area.classList.remove('selected-component'));
            }

            function resetInfoArea() {
                initialMessageEl.style.display = 'block';
                componentInfoEl.style.display = 'none';
                troubleshootingAreaEl.style.display = 'none';
                manualTroubleshootingEl.style.display = 'none';
                aiTroubleshootingEl.style.display = 'none';
                malfunctionListEl.innerHTML = '';
                if (currentAiAnimationTimeout) {
                    clearTimeout(currentAiAnimationTimeout);
                    currentAiAnimationTimeout = null;
                }
                aiLogEl.innerHTML = '';
                aiRecommendationEl.textContent = '';
            }
            
            // Initial state
            resetInfoArea();


            componentAreas.forEach(area => {
                area.addEventListener('click', () => {
                    clearSelections();
                    area.classList.add('selected-component');
                    
                    const componentKey = area.dataset.component;
                    const componentData = medicalDeviceData.ekg.components[componentKey];

                    initialMessageEl.style.display = 'none';
                    troubleshootingAreaEl.style.display = 'none';
                    componentInfoEl.style.display = 'block';

                    componentNameEl.textContent = componentData.name;
                    componentDescriptionEl.textContent = componentData.description;
                    
                    malfunctionListEl.innerHTML = ''; // Clear previous malfunctions
                    componentData.malfunctions.forEach((malfunction, index) => {
                        const li = document.createElement('li');
                        
                        const nameSpan = document.createElement('span');
                        nameSpan.className = 'malfunction-name-text';
                        nameSpan.textContent = malfunction.name;
                        li.appendChild(nameSpan);

                        const buttonsDiv = document.createElement('div'); // Container for buttons

                        const manualBtn = document.createElement('button');
                        manualBtn.textContent = 'Manual';
                        manualBtn.classList.add('manual-btn');
                        manualBtn.dataset.componentKey = componentKey;
                        manualBtn.dataset.malfunctionIndex = index;
                        manualBtn.addEventListener('click', handleManualTroubleshooting);
                        buttonsDiv.appendChild(manualBtn);

                        const aiBtn = document.createElement('button');
                        aiBtn.textContent = 'AI Assist';
                        aiBtn.classList.add('ai-btn');
                        aiBtn.dataset.componentKey = componentKey;
                        aiBtn.dataset.malfunctionIndex = index;
                        aiBtn.addEventListener('click', handleAiTroubleshooting);
                        buttonsDiv.appendChild(aiBtn);
                        
                        li.appendChild(buttonsDiv);
                        malfunctionListEl.appendChild(li);
                    });
                });
            });

            function handleManualTroubleshooting(event) {
                const componentKey = event.target.dataset.componentKey;
                const malfunctionIndex = parseInt(event.target.dataset.malfunctionIndex);
                const malfunction = medicalDeviceData.ekg.components[componentKey].malfunctions[malfunctionIndex];

                componentInfoEl.style.display = 'none';
                troubleshootingAreaEl.style.display = 'block';
                aiTroubleshootingEl.style.display = 'none';
                manualTroubleshootingEl.style.display = 'block';

                if (currentAiAnimationTimeout) {
                    clearTimeout(currentAiAnimationTimeout);
                    currentAiAnimationTimeout = null;
                }
                aiLogEl.innerHTML = ''; // Clear AI log if it was visible
                aiRecommendationEl.textContent = '';


                malfunctionTitleEl.textContent = malfunction.name;
                manualStepsEl.textContent = malfunction.manualSteps;
            }

            function handleAiTroubleshooting(event) {
                const componentKey = event.target.dataset.componentKey;
                const malfunctionIndex = parseInt(event.target.dataset.malfunctionIndex);
                const malfunction = medicalDeviceData.ekg.components[componentKey].malfunctions[malfunctionIndex];

                componentInfoEl.style.display = 'none';
                troubleshootingAreaEl.style.display = 'block';
                manualTroubleshootingEl.style.display = 'none';
                aiTroubleshootingEl.style.display = 'block';
                
                malfunctionTitleEl.textContent = malfunction.name;
                
                // Clear previous AI log and cancel any ongoing animation
                if (currentAiAnimationTimeout) {
                    clearTimeout(currentAiAnimationTimeout);
                }
                aiLogEl.innerHTML = '';
                aiRecommendationEl.textContent = 'AI is analyzing...';

                animateAiLog(malfunction.aiDiagnosis.log, malfunction.aiDiagnosis.recommendation);
            }

            function animateAiLog(logSteps, recommendation) {
                let i = 0;
                function nextStep() {
                    if (i < logSteps.length) {
                        const p = document.createElement('p');
                        p.textContent = logSteps[i];
                        aiLogEl.appendChild(p);
                        aiLogEl.scrollTop = aiLogEl.scrollHeight;
                        i++;
                        currentAiAnimationTimeout = setTimeout(nextStep, 700);
                    } else {
                        aiRecommendationEl.textContent = "AI Recommendation: " + recommendation;
                        currentAiAnimationTimeout = null; // Animation finished
                    }
                }
                nextStep();
            }
        });
    </script>
</body>
</html>
