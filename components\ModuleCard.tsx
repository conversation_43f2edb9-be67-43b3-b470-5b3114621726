
import React from 'react';
import { Link } from 'react-router-dom';
import { Module, LearningTrack } from '../types';
import { ChevronRightIcon } from './icons/ModuleIcons';

interface ModuleCardProps {
  module: Module;
}

const getTrackColor = (track: LearningTrack): string => {
  switch (track) {
    case LearningTrack.CLINICAL:
      return 'bg-sky-500';
    case LearningTrack.ENGINEERING:
      return 'bg-emerald-500';
    case LearningTrack.BOTH:
      return 'bg-indigo-500';
    default:
      return 'bg-gray-500';
  }
};

const ModuleCard: React.FC<ModuleCardProps> = ({ module }) => {
  const IconComponent = module.icon;

  return (
    <Link to={`/module/${module.id}`} className="block group">
      <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out h-full flex flex-col overflow-hidden">
        <div className={`p-5 flex-grow flex flex-col`}>
          <div className="flex items-start space-x-3 mb-3">
            {IconComponent && <IconComponent className="h-10 w-10 text-primary-600 mt-1" />}
            <div>
              <h3 className="text-xl font-semibold text-primary-700 group-hover:text-primary-800 transition-colors duration-300">
                {module.title}
              </h3>
              <span className={`text-xs font-medium px-2 py-0.5 rounded-full text-white ${getTrackColor(module.learningTrack)}`}>
                {module.learningTrack}
              </span>
            </div>
          </div>
          <p className="text-neutral-600 text-sm mb-3 flex-grow">{module.description}</p>
          <div className="mt-auto">
            <div className="flex flex-wrap gap-2 mb-3">
              {module.tags.slice(0, 3).map(tag => (
                <span key={tag} className="text-xs bg-neutral-200 text-neutral-700 px-2 py-1 rounded-full">
                  {tag}
                </span>
              ))}
            </div>
             <div className="text-primary-600 group-hover:text-primary-700 font-medium flex items-center transition-colors duration-300">
              Learn More
              <ChevronRightIcon className="h-5 w-5 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ModuleCard;
