<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Assistant</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f2f5; /* Light gray background */
            color: #333;
            text-align: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .screen {
            background-color: #ffffff; /* White card background */
            padding: 25px 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%; /* Responsive width */
            margin-bottom: 20px; 
        }

        h1 {
            color: #1c1e21; /* Darker heading */
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 28px;
        }

        p {
            color: #555e69; /* Softer text color */
            line-height: 1.6;
            margin-bottom: 25px;
            font-size: 16px;
        }

        button {
            background-color: #007bff; /* Primary blue */
            color: white;
            border: none;
            padding: 12px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            font-weight: 500;
            margin: 8px 4px;
            cursor: pointer;
            border-radius: 6px;
            transition: background-color 0.2s ease-in-out, transform 0.1s ease;
            min-width: 100px;
        }

        button:hover {
            background-color: #0056b3; /* Darker blue on hover */
        }
        button:active {
            transform: translateY(1px); /* Slight press effect */
        }

        #yesButton {
            background-color: #28a745; /* Green for Yes */
        }
        #yesButton:hover {
            background-color: #1e7e34; /* Darker green */
        }

        #noButton {
            background-color: #dc3545; /* Red for No */
        }
        #noButton:hover {
            background-color: #b02a37; /* Darker red */
        }
        
        #restartButton {
            background-color: #6c757d; /* Gray for restart */
        }
        #restartButton:hover {
            background-color: #545b62; /* Darker gray */
        }

        #questionText {
            font-size: 18px;
            margin-bottom: 30px;
            font-weight: 500;
            color: #333; /* Ensure good contrast for question text */
        }

        #diagnosisResult {
            font-size: 20px;
            font-weight: bold; 
            margin-bottom: 25px;
            color: #007bff; /* Default color for specific diagnosis */
        }
        
        #diagnosisResult.default-message {
            color: #555e69; /* Softer color for "Unable to diagnose" message */
            font-weight: normal; /* Less emphasis for default message */
        }

        /* Hide elements by default, JS will manage visibility */
        #questionScreen, #diagnosisScreen {
            display: none;
        }

        .button-container {
            display: flex;
            justify-content: center; /* Center buttons horizontally */
            gap: 15px; /* Space between Yes/No buttons */
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            h1 {
                font-size: 24px;
            }
            p {
                font-size: 15px;
            }
            button {
                font-size: 15px;
                padding: 10px 18px;
                width: 100%; /* Make buttons full width on small screens */
                margin: 8px 0; /* Adjust margin for stacked buttons */
            }
            .button-container {
                flex-direction: column; /* Stack Yes/No buttons */
                gap: 10px; /* Adjust gap for stacked buttons */
            }
            .screen {
                padding: 20px; /* Slightly reduce padding on small screens */
            }
            #questionText {
                font-size: 17px;
            }
            #diagnosisResult {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>

    <div id="welcomeScreen" class="screen">
        <h1>Diagnostic Assistant</h1>
        <p>Answer the following questions to get a possible diagnosis. This tool is for informational purposes only and does not substitute professional medical advice.</p>
        <button id="startButton">Start Diagnosis</button>
    </div>

    <div id="questionScreen" class="screen">
        <p id="questionText"></p>
        <div class="button-container">
            <button id="yesButton">Yes</button>
            <button id="noButton">No</button>
        </div>
    </div>

    <div id="diagnosisScreen" class="screen">
        <p id="diagnosisResult"></p>
        <button id="restartButton">Start Over</button>
    </div>

    <script>
        const questions = [
            { key: "fever", text: "Are you experiencing a fever (temperature above 100.4°F or 38°C)?" },
            { key: "cough", text: "Do you have a persistent cough?" },
            { key: "muscleAches", text: "Are you experiencing muscle aches or body aches?" },
            { key: "soreThroat", text: "Do you have a sore throat?" },
            { key: "fatigue", text: "Are you experiencing fatigue or unusual tiredness?" },
            { key: "headache", text: "Do you have a headache?" }
        ];

        let currentQuestionIndex = 0;
        let userAnswers = {}; // Stores answers as { key: boolean }

        // DOM Elements
        const welcomeScreenElement = document.getElementById('welcomeScreen');
        const questionScreenElement = document.getElementById('questionScreen');
        const diagnosisScreenElement = document.getElementById('diagnosisScreen');

        const startButton = document.getElementById('startButton');
        const questionTextElement = document.getElementById('questionText');
        const yesButton = document.getElementById('yesButton');
        const noButton = document.getElementById('noButton');
        const diagnosisResultElement = document.getElementById('diagnosisResult');
        const restartButton = document.getElementById('restartButton');

        function showScreen(screenElement) {
            // Hide all screens first
            welcomeScreenElement.style.display = 'none';
            questionScreenElement.style.display = 'none';
            diagnosisScreenElement.style.display = 'none';
            // Show the target screen
            screenElement.style.display = 'block';
        }

        function displayCurrentQuestion() {
            if (currentQuestionIndex < questions.length) {
                questionTextElement.textContent = questions[currentQuestionIndex].text;
            } else {
                // All questions answered, proceed to diagnosis
                calculateAndShowDiagnosis();
            }
        }

        function handleAnswer(answerBoolean) {
            if (currentQuestionIndex < questions.length) {
                const currentQuestionKey = questions[currentQuestionIndex].key;
                userAnswers[currentQuestionKey] = answerBoolean;
                currentQuestionIndex++;
                displayCurrentQuestion();
            }
        }

        function calculateAndShowDiagnosis() {
            let diagnosisMessage = "Unable to provide a specific diagnosis. Please consult a healthcare professional.";
            let isSpecificDiagnosis = false;

            // Destructure answers. Default to false if a key is missing (though current flow ensures all keys are present).
            const { 
                fever = false, 
                cough = false, 
                muscleAches = false, 
                soreThroat = false, 
                fatigue = false, 
                headache = false 
            } = userAnswers;

            // Diagnosis Rules (applied sequentially - first match wins)
            // (a) If "Yes" to fever, cough, and fatigue -> Influenza
            if (fever && cough && fatigue) {
                diagnosisMessage = "Possible Diagnosis: Influenza (Flu)";
                isSpecificDiagnosis = true;
            } 
            // (b) If "Yes" to cough, sore throat, and headache -> Common Cold
            else if (cough && soreThroat && headache) {
                diagnosisMessage = "Possible Diagnosis: Common Cold";
                isSpecificDiagnosis = true;
            } 
            // (c) If "Yes" to fever, muscle aches, and fatigue -> Viral Infection
            else if (fever && muscleAches && fatigue) {
                diagnosisMessage = "Possible Diagnosis: Viral Infection";
                isSpecificDiagnosis = true;
            }

            diagnosisResultElement.textContent = diagnosisMessage;
            if (isSpecificDiagnosis) {
                diagnosisResultElement.classList.remove('default-message');
            } else {
                diagnosisResultElement.classList.add('default-message');
            }
            
            showScreen(diagnosisScreenElement);
        }

        function resetAppAndShowWelcome() {
            currentQuestionIndex = 0;
            userAnswers = {};
            diagnosisResultElement.textContent = ''; // Clear previous diagnosis text
            diagnosisResultElement.classList.remove('default-message'); // Reset class
            showScreen(welcomeScreenElement);
        }

        // Event Listeners
        startButton.addEventListener('click', () => {
            currentQuestionIndex = 0; // Reset question index for a new session
            userAnswers = {};         // Clear previous answers for a new session
            showScreen(questionScreenElement);
            displayCurrentQuestion();
        });

        yesButton.addEventListener('click', () => handleAnswer(true));
        noButton.addEventListener('click', () => handleAnswer(false));

        restartButton.addEventListener('click', resetAppAndShowWelcome);

        // Initial setup: Show welcome screen on page load
        showScreen(welcomeScreenElement);

    </script>

</body>
</html>
