
import React from 'react';
import { LearningTrack } from '../types';

interface TrackSelectorProps {
  currentTrack: LearningTrack;
  onTrackChange: (track: LearningTrack) => void;
}

const TrackSelector: React.FC<TrackSelectorProps> = ({ currentTrack, onTrackChange }) => {
  const tracks = Object.values(LearningTrack);

  return (
    <div className="flex items-center space-x-2 mt-4 sm:mt-0">
      <span className="text-sm font-medium text-primary-100">Learning Path:</span>
      <div className="relative">
        <select
          value={currentTrack}
          onChange={(e) => onTrackChange(e.target.value as LearningTrack)}
          className="bg-primary-600 hover:bg-primary-500 text-white border border-primary-400 rounded-md py-2 px-3 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-primary-300 cursor-pointer"
        >
          {tracks.map(track => (
            <option key={track} value={track} className="bg-primary-700 text-white">
              {track}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default TrackSelector;
