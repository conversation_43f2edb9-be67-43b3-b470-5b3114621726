import React, { useState } from 'react';
import { CameraIcon, MagnifyingGlassIcon, CpuChipIcon } from '../icons/ModuleIcons';

interface ImagingModality {
  id: string;
  name: string;
  description: string;
  physics: string;
  applications: string[];
  aiApplications: string[];
  advantages: string[];
  limitations: string[];
  imageCharacteristics: string;
}

interface MedicalImagingExplorerProps {
  moduleTitle: string;
}

const imagingModalities: ImagingModality[] = [
  {
    id: 'xray',
    name: 'X-ray Radiography',
    description: 'Uses ionizing radiation to create 2D images of internal structures.',
    physics: 'X-rays are absorbed differently by various tissues based on their density and atomic number.',
    applications: ['Bone fractures', 'Chest imaging', 'Dental imaging', 'Mammography'],
    aiApplications: ['Automated fracture detection', 'Pneumonia screening', 'Bone age assessment', 'Quality control'],
    advantages: ['Fast acquisition', 'Widely available', 'Cost-effective', 'Good for dense structures'],
    limitations: ['2D projection', 'Radiation exposure', 'Poor soft tissue contrast', 'Overlapping structures'],
    imageCharacteristics: 'High contrast between bone and soft tissue, 2D projection images'
  },
  {
    id: 'ct',
    name: 'Computed Tomography (CT)',
    description: 'Uses X-rays and computer processing to create cross-sectional images.',
    physics: 'Multiple X-ray projections are acquired and reconstructed using mathematical algorithms.',
    applications: ['Trauma imaging', 'Cancer staging', 'Vascular imaging', 'Emergency diagnosis'],
    aiApplications: ['Automated organ segmentation', 'Lesion detection', 'Dose optimization', 'Image reconstruction'],
    advantages: ['3D imaging', 'Excellent bone detail', 'Fast acquisition', 'Widely available'],
    limitations: ['Radiation exposure', 'Contrast agents needed', 'Artifacts from metal', 'Limited soft tissue contrast'],
    imageCharacteristics: 'Cross-sectional grayscale images with excellent spatial resolution'
  },
  {
    id: 'mri',
    name: 'Magnetic Resonance Imaging (MRI)',
    description: 'Uses magnetic fields and radio waves to create detailed images of soft tissues.',
    physics: 'Hydrogen atoms in the body align with magnetic field and emit signals when excited by radio waves.',
    applications: ['Brain imaging', 'Spinal cord', 'Joint imaging', 'Cardiac imaging'],
    aiApplications: ['Brain segmentation', 'Tumor detection', 'Accelerated imaging', 'Quantitative analysis'],
    advantages: ['No radiation', 'Excellent soft tissue contrast', 'Multiple contrast mechanisms', 'Functional imaging'],
    limitations: ['Long scan times', 'Expensive', 'Contraindications', 'Susceptible to motion'],
    imageCharacteristics: 'High soft tissue contrast with multiple image contrasts (T1, T2, FLAIR)'
  },
  {
    id: 'ultrasound',
    name: 'Ultrasound',
    description: 'Uses high-frequency sound waves to create real-time images.',
    physics: 'Sound waves reflect off tissue interfaces and are detected to form images.',
    applications: ['Obstetrics', 'Cardiac imaging', 'Abdominal imaging', 'Vascular studies'],
    aiApplications: ['Automated measurements', 'Organ recognition', 'Doppler analysis', 'Image guidance'],
    advantages: ['Real-time imaging', 'No radiation', 'Portable', 'Cost-effective'],
    limitations: ['Operator dependent', 'Limited by gas/bone', 'Poor image quality in obese patients', 'Acoustic windows'],
    imageCharacteristics: 'Real-time grayscale images with Doppler color overlay for blood flow'
  },
  {
    id: 'pet',
    name: 'Positron Emission Tomography (PET)',
    description: 'Uses radioactive tracers to visualize metabolic processes.',
    physics: 'Positron-emitting radiopharmaceuticals are detected after annihilation with electrons.',
    applications: ['Cancer imaging', 'Cardiac viability', 'Brain metabolism', 'Infection imaging'],
    aiApplications: ['Automated lesion detection', 'SUV quantification', 'Prognostic modeling', 'Image reconstruction'],
    advantages: ['Functional imaging', 'Whole-body capability', 'Quantitative', 'High sensitivity'],
    limitations: ['Radiation exposure', 'Limited resolution', 'Expensive', 'Requires cyclotron'],
    imageCharacteristics: 'Functional images showing metabolic activity with color-coded intensity maps'
  }
];

const MedicalImagingExplorer: React.FC<MedicalImagingExplorerProps> = ({ moduleTitle }) => {
  const [selectedModality, setSelectedModality] = useState<ImagingModality>(imagingModalities[0]);
  const [activeTab, setActiveTab] = useState<'overview' | 'ai' | 'comparison'>('overview');

  const renderModalityOverview = () => (
    <div className="space-y-6">
      <div className="bg-primary-50 p-4 rounded-lg">
        <h4 className="font-semibold text-primary-700 mb-2">Description</h4>
        <p className="text-neutral-700">{selectedModality.description}</p>
      </div>
      
      <div className="bg-secondary-50 p-4 rounded-lg">
        <h4 className="font-semibold text-secondary-700 mb-2">Physics Principles</h4>
        <p className="text-neutral-700">{selectedModality.physics}</p>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-semibold text-green-700 mb-2">Advantages</h4>
          <ul className="list-disc list-inside text-sm text-neutral-700 space-y-1">
            {selectedModality.advantages.map((advantage, index) => (
              <li key={index}>{advantage}</li>
            ))}
          </ul>
        </div>
        
        <div className="bg-red-50 p-4 rounded-lg">
          <h4 className="font-semibold text-red-700 mb-2">Limitations</h4>
          <ul className="list-disc list-inside text-sm text-neutral-700 space-y-1">
            {selectedModality.limitations.map((limitation, index) => (
              <li key={index}>{limitation}</li>
            ))}
          </ul>
        </div>
      </div>

      <div className="bg-neutral-50 p-4 rounded-lg">
        <h4 className="font-semibold text-neutral-700 mb-2">Clinical Applications</h4>
        <div className="flex flex-wrap gap-2">
          {selectedModality.applications.map((application, index) => (
            <span key={index} className="bg-neutral-200 text-neutral-700 px-3 py-1 rounded-full text-sm">
              {application}
            </span>
          ))}
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-semibold text-blue-700 mb-2">Image Characteristics</h4>
        <p className="text-neutral-700">{selectedModality.imageCharacteristics}</p>
      </div>
    </div>
  );

  const renderAIApplications = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-lg">
        <div className="flex items-center mb-4">
          <CpuChipIcon className="h-8 w-8 text-purple-600 mr-3" />
          <h4 className="text-xl font-semibold text-purple-700">AI Applications in {selectedModality.name}</h4>
        </div>
        <div className="grid gap-4">
          {selectedModality.aiApplications.map((application, index) => (
            <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-purple-100">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                <span className="font-medium text-neutral-700">{application}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h4 className="font-semibold text-yellow-700 mb-2">Implementation Considerations</h4>
        <ul className="list-disc list-inside text-sm text-neutral-700 space-y-1">
          <li>Data quality and standardization requirements</li>
          <li>Regulatory approval and clinical validation</li>
          <li>Integration with existing PACS systems</li>
          <li>Training and workflow adaptation</li>
          <li>Performance monitoring and quality assurance</li>
        </ul>
      </div>
    </div>
  );

  const renderComparison = () => (
    <div className="space-y-6">
      <div className="bg-neutral-50 p-4 rounded-lg">
        <h4 className="font-semibold text-neutral-700 mb-4">Modality Comparison Matrix</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-neutral-300">
                <th className="text-left p-2 font-medium">Modality</th>
                <th className="text-left p-2 font-medium">Radiation</th>
                <th className="text-left p-2 font-medium">Soft Tissue</th>
                <th className="text-left p-2 font-medium">Speed</th>
                <th className="text-left p-2 font-medium">Cost</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-neutral-200">
                <td className="p-2 font-medium">X-ray</td>
                <td className="p-2">Yes (Low)</td>
                <td className="p-2">Poor</td>
                <td className="p-2">Very Fast</td>
                <td className="p-2">Low</td>
              </tr>
              <tr className="border-b border-neutral-200">
                <td className="p-2 font-medium">CT</td>
                <td className="p-2">Yes (Moderate)</td>
                <td className="p-2">Good</td>
                <td className="p-2">Fast</td>
                <td className="p-2">Moderate</td>
              </tr>
              <tr className="border-b border-neutral-200">
                <td className="p-2 font-medium">MRI</td>
                <td className="p-2">None</td>
                <td className="p-2">Excellent</td>
                <td className="p-2">Slow</td>
                <td className="p-2">High</td>
              </tr>
              <tr className="border-b border-neutral-200">
                <td className="p-2 font-medium">Ultrasound</td>
                <td className="p-2">None</td>
                <td className="p-2">Good</td>
                <td className="p-2">Real-time</td>
                <td className="p-2">Low</td>
              </tr>
              <tr>
                <td className="p-2 font-medium">PET</td>
                <td className="p-2">Yes (High)</td>
                <td className="p-2">Functional</td>
                <td className="p-2">Slow</td>
                <td className="p-2">Very High</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="border border-neutral-300 rounded-lg shadow-md bg-white">
      <div className="p-4 border-b border-neutral-200">
        <div className="flex items-center mb-2">
          <CameraIcon className="h-6 w-6 text-primary-600 mr-2" />
          <h3 className="text-lg font-semibold text-primary-700">Medical Imaging Explorer</h3>
        </div>
        <p className="text-sm text-neutral-600">
          Explore different medical imaging modalities and their AI applications
        </p>
      </div>

      <div className="p-4">
        <div className="mb-6">
          <h4 className="font-medium text-neutral-700 mb-3">Select Imaging Modality:</h4>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {imagingModalities.map((modality) => (
              <button
                key={modality.id}
                onClick={() => setSelectedModality(modality)}
                className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                  selectedModality.id === modality.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                {modality.name}
              </button>
            ))}
          </div>
        </div>

        <div className="mb-4">
          <div className="flex space-x-1 bg-neutral-100 p-1 rounded-lg">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'overview'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-neutral-600 hover:text-neutral-800'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('ai')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'ai'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-neutral-600 hover:text-neutral-800'
              }`}
            >
              AI Applications
            </button>
            <button
              onClick={() => setActiveTab('comparison')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'comparison'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-neutral-600 hover:text-neutral-800'
              }`}
            >
              Comparison
            </button>
          </div>
        </div>

        <div className="min-h-96">
          {activeTab === 'overview' && renderModalityOverview()}
          {activeTab === 'ai' && renderAIApplications()}
          {activeTab === 'comparison' && renderComparison()}
        </div>
      </div>
    </div>
  );
};

export default MedicalImagingExplorer;
