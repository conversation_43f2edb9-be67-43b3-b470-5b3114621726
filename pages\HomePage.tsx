
import React from 'react';
import ModuleCard from '../components/ModuleCard';
import { Module } from '../types';

interface HomePageProps {
  modules: Module[];
}

const HomePage: React.FC<HomePageProps> = ({ modules }) => {
  return (
    <div>
      <h1 className="text-3xl font-bold text-primary-800 mb-2">Welcome to the LMS</h1>
      <p className="text-neutral-600 mb-8">Select a module below to start your learning journey in AI for Medicine and Engineering.</p>
      {modules.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {modules.map(module => (
            <ModuleCard key={module.id} module={module} />
          ))}
        </div>
      ) : (
        <p className="text-center text-neutral-500 py-10">No modules available for the selected track. Please try a different learning path.</p>
      )}
    </div>
  );
};

export default HomePage;
