
import React from 'react';
import ModuleCard from '../components/ModuleCard';
import { Module } from '../types';

interface HomePageProps {
  modules: Module[];
}

const HomePage: React.FC<HomePageProps> = ({ modules }) => {
  const htmlModules = modules.filter(module => module.interactiveComponentKey === 'HTMLModule');
  const reactModules = modules.filter(module => module.interactiveComponentKey !== 'HTMLModule');
  const clinicalModules = modules.filter(module => module.learningTrack === 'Clinical Professionals');
  const engineeringModules = modules.filter(module => module.learningTrack === 'Engineering Professionals');
  const bothModules = modules.filter(module => module.learningTrack === 'All Professionals');

  return (
    <div>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-primary-50 via-secondary-50 to-neutral-50 rounded-2xl p-8 md:p-12 mb-12 overflow-hidden">
        <div className="absolute top-0 right-0 w-64 h-64 bg-primary-100 rounded-full opacity-20 transform translate-x-32 -translate-y-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-secondary-100 rounded-full opacity-20 transform -translate-x-24 translate-y-24"></div>

        <div className="relative z-10 text-center">
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 mb-6 shadow-sm">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
            <span className="text-sm font-medium text-neutral-700">Live Learning Platform</span>
          </div>

          <h1 className="text-4xl md:text-6xl font-bold text-primary-800 mb-6 leading-tight">
            AI in Medicine &<br />
            <span className="text-gradient">Engineering LMS</span>
          </h1>

          <p className="text-xl text-neutral-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Master the future of healthcare technology through interactive simulations, real-world scenarios,
            and cutting-edge AI applications in medical practice and biomedical engineering.
          </p>

          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-6 py-3 shadow-sm">
              <div className="text-2xl font-bold text-primary-600">{modules.length}</div>
              <div className="text-sm text-neutral-600">Learning Modules</div>
            </div>
            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-6 py-3 shadow-sm">
              <div className="text-2xl font-bold text-green-600">{htmlModules.length}</div>
              <div className="text-sm text-neutral-600">Interactive Simulations</div>
            </div>
            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-6 py-3 shadow-sm">
              <div className="text-2xl font-bold text-purple-600">{reactModules.length}</div>
              <div className="text-sm text-neutral-600">Advanced Components</div>
            </div>
          </div>

          <div className="flex flex-wrap justify-center gap-3">
            <span className="inline-flex items-center bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Medical Imaging
            </span>
            <span className="inline-flex items-center bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              AI Diagnostics
            </span>
            <span className="inline-flex items-center bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              BME Applications
            </span>
            <span className="inline-flex items-center bg-orange-100 text-orange-800 text-sm font-medium px-3 py-1 rounded-full">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Device Maintenance
            </span>
          </div>
        </div>
      </div>

      {/* Learning Tracks Overview */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-primary-800 text-center mb-8">Choose Your Learning Path</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-gradient-to-br from-sky-50 to-sky-100 rounded-xl p-6 border border-sky-200">
            <div className="w-12 h-12 bg-sky-500 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-sky-800 mb-2">Clinical Professionals</h3>
            <p className="text-sky-700 mb-4">AI applications in patient care, diagnostics, and clinical decision-making</p>
            <div className="text-2xl font-bold text-sky-600">{clinicalModules.length} modules</div>
          </div>

          <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl p-6 border border-emerald-200">
            <div className="w-12 h-12 bg-emerald-500 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-emerald-800 mb-2">Engineering Professionals</h3>
            <p className="text-emerald-700 mb-4">Biomedical device design, maintenance, and AI integration</p>
            <div className="text-2xl font-bold text-emerald-600">{engineeringModules.length} modules</div>
          </div>

          <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-6 border border-indigo-200">
            <div className="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-indigo-800 mb-2">All Professionals</h3>
            <p className="text-indigo-700 mb-4">Cross-disciplinary AI applications and future technologies</p>
            <div className="text-2xl font-bold text-indigo-600">{bothModules.length} modules</div>
          </div>
        </div>
      </div>

      {/* Featured Modules Section */}
      <div className="mb-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-primary-800 mb-4">Featured Learning Modules</h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Explore our comprehensive collection of interactive modules designed to advance your expertise in AI-powered healthcare
          </p>
        </div>

        {/* Interactive HTML Modules */}
        {htmlModules.length > 0 && (
          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-primary-700">Interactive Simulation Modules</h3>
                <span className="ml-3 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full animate-pulse">
                  ✨ Newly Integrated
                </span>
              </div>
              <div className="text-sm text-neutral-500">
                {htmlModules.length} modules available
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 mb-6 border border-green-200">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-green-800 mb-2">Rich Interactive Content</h4>
                  <p className="text-green-700 mb-3">
                    Experience hands-on learning through realistic simulations, AI assistant interactions, and scenario-based training.
                    Each module includes interactive elements, real-time feedback, and practical exercises.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-white text-green-700 text-xs font-medium px-2 py-1 rounded">Real-time Simulations</span>
                    <span className="bg-white text-green-700 text-xs font-medium px-2 py-1 rounded">AI Assistant Practice</span>
                    <span className="bg-white text-green-700 text-xs font-medium px-2 py-1 rounded">Scenario Training</span>
                    <span className="bg-white text-green-700 text-xs font-medium px-2 py-1 rounded">Hands-on Exercises</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {htmlModules.map(module => (
                <ModuleCard key={module.id} module={module} />
              ))}
            </div>
          </div>
        )}

        {/* React-based Advanced Modules */}
        {reactModules.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-primary-700">Advanced Learning Components</h3>
                <span className="ml-3 bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  🚀 AI-Powered
                </span>
              </div>
              <div className="text-sm text-neutral-500">
                {reactModules.length} modules available
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 mb-6 border border-purple-200">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-purple-800 mb-2">Next-Generation Learning Technology</h4>
                  <p className="text-purple-700 mb-3">
                    Built with cutting-edge React components and AI integration. Features dynamic content adaptation,
                    real-time data visualization, and personalized learning experiences.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-white text-purple-700 text-xs font-medium px-2 py-1 rounded">Dynamic Components</span>
                    <span className="bg-white text-purple-700 text-xs font-medium px-2 py-1 rounded">AI Integration</span>
                    <span className="bg-white text-purple-700 text-xs font-medium px-2 py-1 rounded">Data Visualization</span>
                    <span className="bg-white text-purple-700 text-xs font-medium px-2 py-1 rounded">Personalized Learning</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reactModules.map(module => (
                <ModuleCard key={module.id} module={module} />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Learning Resources Section */}
      <div className="mb-12">
        <div className="bg-gradient-to-r from-neutral-50 to-neutral-100 rounded-2xl p-8 border border-neutral-200">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-primary-800 mb-4">Learning Resources & Support</h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Access additional resources to enhance your learning experience
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-neutral-800 mb-2">Documentation</h3>
              <p className="text-neutral-600 text-sm">Comprehensive guides and technical documentation</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-neutral-800 mb-2">Community</h3>
              <p className="text-neutral-600 text-sm">Connect with peers and expert instructors</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-neutral-800 mb-2">Progress Tracking</h3>
              <p className="text-neutral-600 text-sm">Monitor your learning progress and achievements</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-neutral-800 mb-2">Support</h3>
              <p className="text-neutral-600 text-sm">Get help when you need it from our support team</p>
            </div>
          </div>
        </div>
      </div>

      {modules.length === 0 && (
        <div className="text-center py-16">
          <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-12 h-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-neutral-700 mb-2">No Modules Available</h3>
          <p className="text-neutral-500 mb-6">No modules are available for the selected learning track.</p>
          <p className="text-sm text-neutral-400">Please try selecting a different learning path or contact support for assistance.</p>
        </div>
      )}
    </div>
  );
};

export default HomePage;
