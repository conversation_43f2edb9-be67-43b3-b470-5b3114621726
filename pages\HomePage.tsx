
import React from 'react';
import ModuleCard from '../components/ModuleCard';
import { Module } from '../types';

interface HomePageProps {
  modules: Module[];
}

const HomePage: React.FC<HomePageProps> = ({ modules }) => {
  const htmlModules = modules.filter(module => module.interactiveComponentKey === 'HTMLModule');
  const reactModules = modules.filter(module => module.interactiveComponentKey !== 'HTMLModule');

  return (
    <div>
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-primary-800 mb-4">AI in Medicine & Engineering LMS</h1>
        <p className="text-lg text-neutral-600 mb-6">
          Comprehensive learning platform featuring interactive modules, simulations, and hands-on exercises
        </p>
        <div className="flex justify-center space-x-8 text-sm text-neutral-500">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span>{modules.length} Total Modules</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span>{htmlModules.length} Interactive HTML Modules</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
            <span>{reactModules.length} React-based Modules</span>
          </div>
        </div>
      </div>

      {htmlModules.length > 0 && (
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <h2 className="text-2xl font-bold text-primary-700 mr-3">Interactive HTML Modules</h2>
            <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              Newly Integrated
            </span>
          </div>
          <p className="text-neutral-600 mb-6">
            These modules feature rich interactive content with simulations, scenarios, and hands-on exercises.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {htmlModules.map(module => (
              <ModuleCard key={module.id} module={module} />
            ))}
          </div>
        </div>
      )}

      {reactModules.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-primary-700 mb-6">React-based Learning Modules</h2>
          <p className="text-neutral-600 mb-6">
            Advanced modules with custom React components and AI-powered features.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {reactModules.map(module => (
              <ModuleCard key={module.id} module={module} />
            ))}
          </div>
        </div>
      )}

      {modules.length === 0 && (
        <p className="text-center text-neutral-500 py-10">No modules available for the selected track. Please try a different learning path.</p>
      )}
    </div>
  );
};

export default HomePage;
