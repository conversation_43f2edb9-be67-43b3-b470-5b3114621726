
import { GoogleGenAI, GenerateContentResponse, Chat } from "@google/genai";

// IMPORTANT: API key must be set as an environment variable `process.env.API_KEY`
// Do NOT embed the API key directly in the code.
// The application assumes this environment variable is configured externally.
const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  console.warn(
    "Gemini API key not found. Please set the API_KEY environment variable. AI features will be limited."
  );
}

// Initialize the Google AI client if API_KEY is available
const ai = API_KEY ? new GoogleGenAI({ apiKey: API_KEY }) : null;

export const generateText = async (prompt: string, systemInstruction?: string): Promise<string> => {
  if (!ai) {
    return "AI service is not available. API key might be missing.";
  }
  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: 'gemini-2.5-flash-preview-04-17', // Recommended model
      contents: prompt,
      config: {
        ...(systemInstruction && { systemInstruction }),
        // thinkingConfig: { thinkingBudget: 0 } // Uncomment for low latency if needed
      }
    });
    return response.text;
  } catch (error) {
    console.error("Error generating text from Gemini:", error);
    if (error instanceof Error) {
        return `Error from AI: ${error.message}`;
    }
    return "An unexpected error occurred while communicating with the AI.";
  }
};


let chatInstance: Chat | null = null;

export const startOrGetChat = (systemInstruction?: string): Chat | null => {
    if (!ai) {
        console.warn("AI client not initialized, cannot create chat.");
        return null;
    }
    if (!chatInstance) { // Create a new chat instance if one doesn't exist
        chatInstance = ai.chats.create({
            model: 'gemini-2.5-flash-preview-04-17',
            config: {
                ...(systemInstruction && { systemInstruction }),
            },
        });
    } else if (systemInstruction && chatInstance) {
        // This is a simplification. In a real app, you might want to re-create the chat
        // if the system instruction changes significantly or provide a way to update it.
        // For now, we'll log a warning if trying to change instruction on an existing chat.
        console.warn("Chat instance already exists. System instruction cannot be changed on an active chat this way. Restart chat for new instruction.");
    }
    return chatInstance;
};

export const sendMessageInChat = async (message: string, systemInstruction?: string): Promise<string> => {
    if (!API_KEY) return "AI service is not available. API key might be missing.";
    
    const chat = startOrGetChat(systemInstruction);
    if (!chat) {
        return "Failed to initialize chat with AI.";
    }

    try {
        const response: GenerateContentResponse = await chat.sendMessage({ message });
        return response.text;
    } catch (error) {
        console.error("Error sending message to Gemini Chat:", error);
        if (error instanceof Error) {
            return `Error from AI: ${error.message}`;
        }
        return "An unexpected error occurred while communicating with the AI chat.";
    }
};

export const resetChat = () => {
    chatInstance = null;
};

// Placeholder for image generation if needed in future
export const generateImage = async (prompt: string): Promise<string | null> => {
  if (!ai) {
    return "AI service is not available for image generation.";
  }
  try {
    const response = await ai.models.generateImages({
        model: 'imagen-3.0-generate-002',
        prompt: prompt,
        config: {numberOfImages: 1, outputMimeType: 'image/jpeg'},
    });

    if (response.generatedImages && response.generatedImages.length > 0) {
        const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
        return `data:image/jpeg;base64,${base64ImageBytes}`;
    }
    return "No image generated.";
  } catch (error) {
    console.error("Error generating image from Gemini:", error);
    return "Error generating image.";
  }
};

export const isGeminiAvailable = (): boolean => {
  return !!API_KEY && !!ai;
};
