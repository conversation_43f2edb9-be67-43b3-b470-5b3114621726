import React, { useState, useEffect } from 'react';
import { ArrowLeftIcon, ExternalLinkIcon, RefreshIcon } from '../icons/ModuleIcons';

interface HTMLModuleViewerProps {
  moduleTitle: string;
  htmlFileName: string;
  description?: string;
}

const HTMLModuleViewer: React.FC<HTMLModuleViewerProps> = ({ 
  moduleTitle, 
  htmlFileName, 
  description 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const refreshIframe = () => {
    setIsLoading(true);
    setHasError(false);
    // Force iframe reload by changing src
    const iframe = document.getElementById('html-module-iframe') as HTMLIFrameElement;
    if (iframe) {
      const currentSrc = iframe.src;
      iframe.src = '';
      setTimeout(() => {
        iframe.src = currentSrc;
      }, 100);
    }
  };

  const openInNewTab = () => {
    window.open(htmlFileName, '_blank');
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  useEffect(() => {
    // Reset states when module changes
    setIsLoading(true);
    setHasError(false);
    setIsFullscreen(false);
  }, [htmlFileName]);

  return (
    <div className={`border border-neutral-300 rounded-lg shadow-md bg-white ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      <div className="p-4 border-b border-neutral-200 bg-neutral-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-primary-700">{moduleTitle}</h3>
            {isFullscreen && (
              <button
                onClick={toggleFullscreen}
                className="ml-4 p-2 text-neutral-600 hover:text-neutral-800 transition-colors"
                title="Exit Fullscreen"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={refreshIframe}
              className="p-2 text-neutral-600 hover:text-primary-600 transition-colors"
              title="Refresh Module"
              disabled={isLoading}
            >
              <RefreshIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            
            <button
              onClick={openInNewTab}
              className="p-2 text-neutral-600 hover:text-primary-600 transition-colors"
              title="Open in New Tab"
            >
              <ExternalLinkIcon className="h-5 w-5" />
            </button>
            
            {!isFullscreen && (
              <button
                onClick={toggleFullscreen}
                className="px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                title="Fullscreen Mode"
              >
                Fullscreen
              </button>
            )}
          </div>
        </div>
        
        {description && (
          <p className="text-sm text-neutral-600 mt-2">{description}</p>
        )}
      </div>

      <div className={`relative ${isFullscreen ? 'h-full' : 'h-96 md:h-[600px]'}`}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-50">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"></div>
              <p className="text-neutral-600">Loading interactive module...</p>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-50">
            <div className="text-center p-6">
              <div className="text-red-500 mb-4">
                <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-neutral-700 mb-2">Module Loading Error</h4>
              <p className="text-neutral-600 mb-4">
                Unable to load the interactive module. This might be due to a network issue or the file being unavailable.
              </p>
              <div className="space-x-2">
                <button
                  onClick={refreshIframe}
                  className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={openInNewTab}
                  className="px-4 py-2 bg-neutral-600 text-white rounded hover:bg-neutral-700 transition-colors"
                >
                  Open Directly
                </button>
              </div>
            </div>
          </div>
        )}

        <iframe
          id="html-module-iframe"
          src={htmlFileName}
          className={`w-full h-full border-0 rounded-b-lg ${isLoading || hasError ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          title={moduleTitle}
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          loading="lazy"
        />
      </div>

      {isFullscreen && (
        <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
          Press ESC or click the back arrow to exit fullscreen
        </div>
      )}
    </div>
  );
};

export default HTMLModuleViewer;
