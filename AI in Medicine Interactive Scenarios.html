<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medicine: Interactive Scenarios</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }

        header h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }

        #progress-tracker-container {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }

        #progress-text {
            text-align: center;
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }

        .progress-bar-outline {
            width: 100%;
            background-color: #ced4da;
            border-radius: 5px;
            height: 24px;
            overflow: hidden;
        }

        #progress-bar-fill {
            height: 100%;
            width: 0%;
            background-color: #28a745; /* Green for progress */
            border-radius: 5px;
            transition: width 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8em;
        }

        #scenario-list button, .choice-button, #next-step-button, .option-button {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin: 10px 0;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        #scenario-list button:hover, .choice-button:hover, #next-step-button:hover, .option-button:hover {
            background-color: #0056b3;
        }
        
        .option-button.disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .option-button.correct {
            background-color: #28a745; /* Green */
        }
        .option-button.incorrect {
            background-color: #dc3545; /* Red */
        }


        #scenario-view h2, #scenario-view h3 {
            color: #17a2b8;
            margin-top: 1.5em;
        }
        #scenario-view h2:first-child, #scenario-view h3:first-child {
            margin-top: 0;
        }

        #scenario-description, #ai-tool-description, #question-text, #feedback-text, #path-conclusion-text, #scenario-summary-content {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #eef;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        
        #ai-assistance-text {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #d1ecf1; /* Light blue for AI info */
            border-left: 4px solid #17a2b8;
            border-radius: 4px;
            font-style: italic;
        }

        #feedback-text {
            font-weight: bold;
        }
        #feedback-text.correct {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        #feedback-text.incorrect {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 600px) {
            .container {
                width: 95%;
                padding: 15px;
            }
            header h1 {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>AI in Medicine: Interactive Scenarios</h1>
        </header>

        <div id="progress-tracker-container">
            <p id="progress-text">Scenarios Completed: 0/3</p>
            <div class="progress-bar-outline">
                <div id="progress-bar-fill">0%</div>
            </div>
        </div>

        <div id="main-content">
            <div id="scenario-list">
                <!-- Scenario buttons will be populated here -->
            </div>

            <div id="scenario-view" class="hidden">
                <h2 id="scenario-title"></h2>
                <p id="scenario-description"></p>

                <div id="initial-choice-buttons">
                    <button class="choice-button" id="use-ai-button">Use AI Assistance</button>
                    <button class="choice-button" id="no-ai-button">Proceed Without AI</button>
                </div>

                <div id="question-area" class="hidden">
                    <h3 id="current-task-header">Current Task</h3>
                    <p id="ai-tool-description" class="hidden"></p>
                    <p id="question-text"></p>
                    <p id="ai-assistance-text" class="hidden"></p>
                    <div id="options-container">
                        <!-- Option buttons will be populated here -->
                    </div>
                    <p id="feedback-text" class="hidden"></p>
                </div>

                <div id="path-conclusion-area" class="hidden">
                    <h3>Path Conclusion</h3>
                    <p id="path-conclusion-text"></p>
                </div>

                <div id="scenario-summary-area" class="hidden">
                    <h3>Scenario Summary & Key Takeaways</h3>
                    <div id="scenario-summary-content"></div>
                </div>
                
                <button id="next-step-button" class="hidden">Next</button>
            </div>
        </div>
    </div>

    <script>
        const scenarios = [
            {
                id: "skin_lesion",
                title: "Scenario 1: Diagnosing a Skin Lesion",
                description: "A 45-year-old patient presents with a mole on their back that has changed in appearance over the past few months. You need to assess its potential for malignancy.",
                withoutAI: {
                    questions: [
                        {
                            questionText: "When evaluating a mole using the ABCDE criteria for melanoma, which of these characteristics is generally considered the MOST concerning?",
                            options: [
                                { text: "Perfectly symmetrical shape", isCorrect: false },
                                { text: "Uniform, single color (e.g., all light brown)", isCorrect: false },
                                { text: "Evolution or change in size, shape, or color", isCorrect: true },
                                { text: "Diameter less than 6mm", isCorrect: false }
                            ],
                            feedbackCorrect: "Correct! Evolution (change) is a critical warning sign for melanoma.",
                            feedbackIncorrect: "Incorrect. Evolution or change in a mole's appearance is a key indicator of potential malignancy."
                        },
                        {
                            questionText: "If a lesion exhibits irregular borders and multiple colors (e.g., shades of brown, black, red), what is your preliminary assessment?",
                            options: [
                                { text: "Likely benign, reassure patient.", isCorrect: false },
                                { text: "Suspicious for malignancy, further investigation needed.", isCorrect: true },
                                { text: "Definitely benign, no follow-up needed.", isCorrect: false },
                                { text: "A common viral wart.", isCorrect: false }
                            ],
                            feedbackCorrect: "Correct! Irregular borders and color variegation are suspicious signs requiring further investigation.",
                            feedbackIncorrect: "Incorrect. These features are concerning for melanoma and warrant further investigation, such as a biopsy."
                        }
                    ],
                    conclusion: "Without AI, diagnosis relies heavily on established clinical criteria and observational skills. Careful application of the ABCDEs is crucial."
                },
                withAI: {
                    aiToolDescription: "You are using 'DermAI Scan v2.0'. It analyzes a digital image of the lesion and provides a malignancy risk score (0-100%) and highlights features like asymmetry, border irregularity, and color variation.",
                    questions: [
                        {
                            questionText: "DermAI Scan analyzes the lesion and reports: Malignancy Risk: 78%. Key features highlighted: Significant Asymmetry, Irregular Borders. How does this AI input primarily influence your next step?",
                            options: [
                                { text: "Ignore the AI, it's probably oversensitive.", isCorrect: false },
                                { text: "Strongly reinforces suspicion of malignancy, prioritize biopsy.", isCorrect: true },
                                { text: "Assume it's benign because the AI isn't 100% certain.", isCorrect: false },
                                { text: "Only rely on the AI if the risk score is above 90%.", isCorrect: false }
                            ],
                            aiAssistance: "AI Output: Malignancy Risk: 78%. Features: Asymmetry (High), Border Irregularity (High), Color Variegation (Moderate).",
                            feedbackCorrect: "Correct! A high-risk score from a validated AI tool, combined with concerning visual features, strongly supports the need for a biopsy.",
                            feedbackIncorrect: "Incorrect. A 78% risk score, especially with highlighted concerning features, should significantly raise suspicion and prompt further action like a biopsy. It's a tool to augment, not replace, judgment but provides strong evidence."
                        },
                        {
                            questionText: "The AI also notes 'low confidence in color analysis due to image glare.' How should you factor this specific AI feedback into your decision-making?",
                            options: [
                                { text: "Dismiss the entire AI report as unreliable.", isCorrect: false },
                                { text: "Rely more heavily on your own visual assessment of color while still considering other AI findings.", isCorrect: true },
                                { text: "Increase the AI's overall malignancy risk score manually.", isCorrect: false },
                                { text: "Immediately retake the image, even if it delays diagnosis significantly.", isCorrect: false }
                            ],
                            aiAssistance: "AI Output: Malignancy Risk: 78%. Features: Asymmetry (High), Border Irregularity (High). Alert: Low confidence in color analysis due to image glare.",
                            feedbackCorrect: "Correct! This highlights the importance of understanding AI limitations. You should use your clinical judgment for the aspect the AI struggled with, while still valuing its input on other features.",
                            feedbackIncorrect: "Incorrect. AI transparency about limitations is valuable. You should critically evaluate the AI's output, using your clinical skills to compensate for areas where the AI has low confidence, rather than dismissing it entirely or blindly following it."
                        }
                    ],
                    conclusion: "With AI, you gain a quantitative risk assessment and feature highlighting. However, it's crucial to understand AI limitations (e.g., image quality issues) and integrate its findings with your clinical judgment."
                },
                summary: {
                    takeaways: "AI in dermatology can act as a 'second opinion,' improve diagnostic accuracy for certain lesions, and help standardize assessments. However, it's not infallible. Clinical expertise remains paramount to interpret AI findings, consider patient context, and manage aspects where AI might be weak (e.g., poor image quality, rare conditions).",
                    ethicalConsiderations: "Key ethical points include: \n1. Algorithmic Bias: Ensuring AI performs equally well across different skin tones and populations. \n2. Data Privacy: Protecting sensitive patient images and data. \n3. Transparency: Understanding how the AI arrives at its conclusions. \n4. Over-reliance: Avoiding deskilling and maintaining clinical judgment."
                }
            },
            {
                id: "diabetes_treatment",
                title: "Scenario 2: Adjusting Diabetes Treatment Plan",
                description: "A 60-year-old patient with Type 2 Diabetes has an HbA1c of 8.5% despite being on Metformin. They also have recently diagnosed moderate chronic kidney disease (CKD stage 3). You need to recommend an adjustment to their treatment plan.",
                withoutAI: {
                    questions: [
                        {
                            questionText: "Given the patient's HbA1c of 8.5% and new diagnosis of CKD stage 3, which class of medication, when added to Metformin, offers both glycemic control and potential renal benefits?",
                            options: [
                                { text: "Sulfonylurea (e.g., Glipizide)", isCorrect: false },
                                { text: "SGLT2 inhibitor (e.g., Empagliflozin)", isCorrect: true },
                                { text: "Thiazolidinedione (e.g., Pioglitazone)", isCorrect: false },
                                { text: "Rapid-acting Insulin", isCorrect: false }
                            ],
                            feedbackCorrect: "Correct! SGLT2 inhibitors are recommended for patients with T2D and CKD due to their proven cardiovascular and renal benefits, in addition to glucose lowering.",
                            feedbackIncorrect: "Incorrect. SGLT2 inhibitors are a preferred choice in this scenario due to their dual benefits for glycemic control and kidney protection in patients with T2D and CKD."
                        },
                        {
                            questionText: "What is a critical lifestyle modification to emphasize for this patient, particularly considering their diabetes and CKD?",
                            options: [
                                { text: "High protein diet for kidney health.", isCorrect: false },
                                { text: "Strict carbohydrate counting and regular moderate exercise.", isCorrect: true },
                                { text: "Increased fluid intake without sodium restriction.", isCorrect: false },
                                { text: "Focus solely on weight loss through any means.", isCorrect: false }
                            ],
                            feedbackCorrect: "Correct! A balanced diet with controlled carbohydrate intake and regular physical activity are cornerstones of diabetes management and can also benefit kidney health.",
                            feedbackIncorrect: "Incorrect. While protein intake needs careful consideration in CKD, strict carbohydrate counting and regular exercise are fundamental for diabetes management and overall health in this context. Sodium restriction is also often important."
                        }
                    ],
                    conclusion: "Traditional management involves applying clinical guidelines, considering comorbidities, and patient preferences. Knowledge of drug classes, their benefits, and side effects is key."
                },
                withAI: {
                    aiToolDescription: "You are using 'GlucoOptimizer AI'. This tool analyzes comprehensive patient data (labs, comorbidities, lifestyle, medication history) and suggests personalized treatment regimens with predicted outcomes (e.g., HbA1c reduction, side effect risks).",
                    questions: [
                        {
                            questionText: "GlucoOptimizer AI processes the patient's data. Top suggestion: 'Add SGLT2 inhibitor (Predicted HbA1c: 7.1%, High renal benefit, Moderate risk of GU infections). Second suggestion: 'Add GLP-1 receptor agonist (Predicted HbA1c: 7.0%, Weight loss benefit, Moderate GI side effects)'. Given the patient's CKD, which AI suggestion aligns best with current guidelines?",
                            options: [
                                { text: "The GLP-1 RA because of slightly better HbA1c prediction.", isCorrect: false },
                                { text: "The SGLT2 inhibitor due to its specific renal benefits.", isCorrect: true },
                                { text: "Neither, the AI must be missing information.", isCorrect: false },
                                { text: "Ask the patient to choose based on side effect profile only.", isCorrect: false }
                            ],
                            aiAssistance: "AI Output: \n1. Add SGLT2i: Pred. HbA1c 7.1%, High renal benefit, Mod. GU infection risk. \n2. Add GLP-1RA: Pred. HbA1c 7.0%, Weight loss, Mod. GI side effects.",
                            feedbackCorrect: "Correct! While both are good options, the explicit 'High renal benefit' for SGLT2 inhibitors makes it particularly suitable for a patient with CKD, aligning with major clinical guidelines.",
                            feedbackIncorrect: "Incorrect. The SGLT2 inhibitor is generally preferred here due to its documented renal protective effects, which is a critical consideration for this patient with CKD."
                        },
                        {
                            questionText: "The AI also flags a 'moderate risk of Genitourinary Infections (GUIs)' with the SGLT2 inhibitor. How should you integrate this AI-provided risk information into your patient counseling?",
                            options: [
                                { text: "Don't mention it to avoid worrying the patient.", isCorrect: false },
                                { text: "Advise against the SGLT2i due to this risk.", isCorrect: false },
                                { text: "Discuss the risk transparently, explain preventive measures, and monitor.", isCorrect: true },
                                { text: "Tell the patient the AI guarantees they will get a GUI.", isCorrect: false }
                            ],
                            aiAssistance: "AI Output (for SGLT2i): ...Moderate risk of Genitourinary Infections (GUIs)...",
                            feedbackCorrect: "Correct! Transparently discussing potential side effects flagged by AI, along with management strategies, is key to shared decision-making and informed consent.",
                            feedbackIncorrect: "Incorrect. AI-identified risks should be discussed openly. For SGLT2 inhibitors, counseling on hygiene and symptoms of GUIs is important, as the benefits often outweigh this manageable risk."
                        }
                    ],
                    conclusion: "AI can process vast amounts of data to personalize recommendations and predict outcomes. It helps in considering multiple factors simultaneously. However, the clinician must validate AI suggestions against guidelines, patient specifics, and discuss risks/benefits."
                },
                summary: {
                    takeaways: "AI in diabetes management can help optimize treatment selection by analyzing complex patient profiles and predicting responses. It can support adherence to guidelines and identify potential risks. Clinicians must use AI as a decision-support tool, always applying clinical judgment, considering patient preferences, and ensuring the 'human touch' in care.",
                    ethicalConsiderations: "Ethical issues include: \n1. Algorithmic Bias: Ensuring treatment recommendations are equitable and not biased by demographic data. \n2. Data Security: Protecting highly personal health information used by the AI. \n3. Patient Autonomy: Balancing AI recommendations with patient choice and values. \n4. Explainability: Understanding why the AI suggests a particular regimen."
                }
            },
            {
                id: "cxr_interpretation",
                title: "Scenario 3: Interpreting a Chest X-ray for Pneumonia",
                description: "A 70-year-old patient presents to the ER with a 3-day history of cough, fever (38.5°C), and increasing shortness of breath. A portable chest X-ray (CXR) has been performed.",
                withoutAI: {
                    questions: [
                        {
                            questionText: "On reviewing the CXR, you observe a dense, opaque area in the right lower lobe that obscures the diaphragm. What is this finding most consistent with?",
                            options: [
                                { text: "Normal lung markings", isCorrect: false },
                                { text: "Lobar consolidation, suggestive of pneumonia", isCorrect: true },
                                { text: "Large pleural effusion", isCorrect: false },
                                { text: "Pneumothorax", isCorrect: false }
                            ],
                            feedbackCorrect: "Correct! A dense opacity in a lobar distribution is characteristic of consolidation, commonly seen in bacterial pneumonia.",
                            feedbackIncorrect: "Incorrect. This description points to lobar consolidation, a hallmark of pneumonia. A large pleural effusion would typically blunt costophrenic angles and create a meniscus sign."
                        },
                        {
                            questionText: "Given the clinical presentation (cough, fever, dyspnea) and the CXR finding of consolidation, what is the most appropriate next step in management?",
                            options: [
                                { text: "Discharge home with advice for bed rest.", isCorrect: false },
                                { text: "Administer supplemental oxygen if SpO2 is low and initiate empiric antibiotics.", isCorrect: true },
                                { text: "Order an urgent CT scan of the chest for better visualization.", isCorrect: false },
                                { text: "Wait for sputum culture results before starting any treatment.", isCorrect: false }
                            ],
                            feedbackCorrect: "Correct! In a patient with clinical signs of pneumonia and CXR confirmation, prompt initiation of antibiotics and supportive care (like oxygen if needed) is crucial.",
                            feedbackIncorrect: "Incorrect. With strong clinical and radiological evidence of pneumonia, empiric antibiotics should be started promptly, along with supportive care. Delaying treatment can worsen outcomes."
                        }
                    ],
                    conclusion: "Interpreting CXRs requires systematic evaluation and pattern recognition. Clinical correlation is essential for accurate diagnosis and management."
                },
                withAI: {
                    aiToolDescription: "You are using 'ChestSight AI'. This AI tool analyzes CXR images, highlights suspicious regions (ROIs), and provides probability scores for common findings like pneumonia, nodules, and pneumothorax.",
                    questions: [
                        {
                            questionText: "ChestSight AI analyzes the CXR. It highlights the right lower lobe with an 85% probability for 'Consolidation/Pneumonia'. It also flags a small, vague opacity in the left mid-lung field with a 20% probability for 'Nodule'. How should you prioritize these findings?",
                            options: [
                                { text: "Focus solely on the high-probability pneumonia, ignore the nodule flag.", isCorrect: false },
                                { text: "Address the acute pneumonia first; note the nodule for follow-up if clinically appropriate after recovery.", isCorrect: true },
                                { text: "Immediately order a CT scan to investigate the low-probability nodule.", isCorrect: false },
                                { text: "Dismiss the AI as it found too many things.", isCorrect: false }
                            ],
                            aiAssistance: "AI Output: \n- Right Lower Lobe: Consolidation/Pneumonia (Probability: 85%). \n- Left Mid-Lung: Nodule (Probability: 20%).",
                            feedbackCorrect: "Correct! The acute, high-probability finding (pneumonia) related to the patient's symptoms takes precedence. The low-probability nodule should be noted for potential future follow-up, but not derail acute management.",
                            feedbackIncorrect: "Incorrect. The AI helps identify multiple findings, but clinical prioritization is key. The symptomatic, high-probability pneumonia needs immediate attention. The incidental, low-probability nodule can be considered for follow-up later."
                        },
                        {
                            questionText: "If the AI's report for pneumonia (85% probability) aligns with your own strong suspicion from viewing the image and the patient's symptoms, how does the AI primarily augment your workflow here?",
                            options: [
                                { text: "It replaces your need to look at the image at all.", isCorrect: false },
                                { text: "It provides confirmatory evidence, potentially increasing diagnostic confidence and speed.", isCorrect: true },
                                { text: "It makes your clinical judgment irrelevant.", isCorrect: false },
                                { text: "It primarily serves to find incidentalomas that complicate care.", isCorrect: false }
                            ],
                            aiAssistance: "AI Output: Right Lower Lobe: Consolidation/Pneumonia (Probability: 85%).",
                            feedbackCorrect: "Correct! AI can act as a 'second reader,' increasing confidence and potentially speeding up the diagnostic process when its findings align with clinical suspicion. It doesn't replace the clinician but supports them.",
                            feedbackIncorrect: "Incorrect. When AI confirms clinical suspicion, it can enhance diagnostic confidence and efficiency. It's a tool for augmentation, not replacement, of clinical skills."
                        }
                    ],
                    conclusion: "AI in radiology can help detect abnormalities, quantify findings, and improve workflow efficiency. It's vital to correlate AI findings with the clinical picture and to be aware of potential false positives or negatives."
                },
                summary: {
                    takeaways: "AI tools for CXR interpretation can improve detection rates for conditions like pneumonia, nodules, or pneumothorax, especially in busy settings or for less experienced readers. They can highlight areas of interest, acting as a preliminary read or a concurrent second opinion. Clinicians must still interpret these findings in the full clinical context and retain final diagnostic responsibility.",
                    ethicalConsiderations: "Key ethical concerns include: \n1. Accountability: Who is responsible if AI misses a critical finding or makes an error? \n2. Deskilling: Over-reliance on AI could erode radiologists' interpretative skills over time. \n3. Image Quality Dependency: AI performance can be heavily affected by the quality of the input CXR. \n4. Bias: AI models might perform differently on images from diverse patient populations or different X-ray equipment."
                }
            }
        ];

        let currentScenario = null;
        let currentPath = null; // 'withAI' or 'withoutAI'
        let currentQuestionIndex = 0;
        let currentPathScore = 0;
        let currentPathTotalQuestions = 0;
        let completedScenarioIDs = new Set();

        const scenarioListDiv = document.getElementById('scenario-list');
        const scenarioViewDiv = document.getElementById('scenario-view');
        const scenarioTitleEl = document.getElementById('scenario-title');
        const scenarioDescriptionEl = document.getElementById('scenario-description');
        const initialChoiceButtonsDiv = document.getElementById('initial-choice-buttons');
        const useAiButton = document.getElementById('use-ai-button');
        const noAiButton = document.getElementById('no-ai-button');
        
        const questionAreaDiv = document.getElementById('question-area');
        const currentTaskHeader = document.getElementById('current-task-header');
        const aiToolDescriptionEl = document.getElementById('ai-tool-description');
        const questionTextEl = document.getElementById('question-text');
        const aiAssistanceTextEl = document.getElementById('ai-assistance-text');
        const optionsContainerDiv = document.getElementById('options-container');
        const feedbackTextEl = document.getElementById('feedback-text');
        
        const pathConclusionAreaDiv = document.getElementById('path-conclusion-area');
        const pathConclusionTextEl = document.getElementById('path-conclusion-text');
        
        const scenarioSummaryAreaDiv = document.getElementById('scenario-summary-area');
        const scenarioSummaryContentEl = document.getElementById('scenario-summary-content');
        
        const nextStepButton = document.getElementById('next-step-button');

        function updateProgressTracker() {
            const completedCount = completedScenarioIDs.size;
            const totalScenarios = scenarios.length;
            const percentage = totalScenarios > 0 ? Math.round((completedCount / totalScenarios) * 100) : 0;
            
            document.getElementById('progress-text').textContent = `Scenarios Completed: ${completedCount}/${totalScenarios}`;
            const progressBarFill = document.getElementById('progress-bar-fill');
            progressBarFill.style.width = percentage + '%';
            progressBarFill.textContent = percentage + '%';
        }

        function renderScenarioList() {
            scenarioListDiv.innerHTML = '<h3>Select a Scenario:</h3>';
            scenarios.forEach(scenario => {
                const button = document.createElement('button');
                button.textContent = scenario.title;
                button.onclick = () => startScenario(scenario.id);
                scenarioListDiv.appendChild(button);
            });
            scenarioListDiv.classList.remove('hidden');
            scenarioViewDiv.classList.add('hidden');
            updateProgressTracker();
        }

        function startScenario(scenarioId) {
            currentScenario = scenarios.find(s => s.id === scenarioId);
            currentPath = null;
            currentQuestionIndex = 0;
            currentPathScore = 0;
            currentPathTotalQuestions = 0;

            scenarioTitleEl.textContent = currentScenario.title;
            scenarioDescriptionEl.textContent = currentScenario.description;

            scenarioListDiv.classList.add('hidden');
            scenarioViewDiv.classList.remove('hidden');
            initialChoiceButtonsDiv.classList.remove('hidden');
            questionAreaDiv.classList.add('hidden');
            pathConclusionAreaDiv.classList.add('hidden');
            scenarioSummaryAreaDiv.classList.add('hidden');
            nextStepButton.classList.add('hidden');
            feedbackTextEl.classList.add('hidden');
            aiToolDescriptionEl.classList.add('hidden');
            aiAssistanceTextEl.classList.add('hidden');
        }

        useAiButton.onclick = () => choosePath(true);
        noAiButton.onclick = () => choosePath(false);

        function choosePath(useAI) {
            currentPath = useAI ? 'withAI' : 'withoutAI';
            currentPathTotalQuestions = currentScenario[currentPath].questions.length;
            
            initialChoiceButtonsDiv.classList.add('hidden');
            questionAreaDiv.classList.remove('hidden');
            currentTaskHeader.textContent = useAI ? "Task with AI Assistance" : "Task without AI Assistance";

            if (useAI && currentScenario.withAI.aiToolDescription) {
                aiToolDescriptionEl.textContent = currentScenario.withAI.aiToolDescription;
                aiToolDescriptionEl.classList.remove('hidden');
            } else {
                aiToolDescriptionEl.classList.add('hidden');
            }
            displayQuestion();
        }

        function displayQuestion() {
            feedbackTextEl.classList.add('hidden');
            aiAssistanceTextEl.classList.add('hidden');
            const questionData = currentScenario[currentPath].questions[currentQuestionIndex];
            questionTextEl.textContent = `Question ${currentQuestionIndex + 1}/${currentPathTotalQuestions}: ${questionData.questionText}`;
            
            if (currentPath === 'withAI' && questionData.aiAssistance) {
                aiAssistanceTextEl.textContent = `AI Insight: ${questionData.aiAssistance}`;
                aiAssistanceTextEl.classList.remove('hidden');
            }

            optionsContainerDiv.innerHTML = '';
            questionData.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.textContent = option.text;
                button.classList.add('option-button');
                button.onclick = () => submitAnswer(index);
                optionsContainerDiv.appendChild(button);
            });
            nextStepButton.classList.add('hidden'); // Hide until answer is submitted
        }

        function submitAnswer(selectedIndex) {
            const questionData = currentScenario[currentPath].questions[currentQuestionIndex];
            const selectedOption = questionData.options[selectedIndex];

            // Disable all option buttons
            const optionButtons = optionsContainerDiv.querySelectorAll('.option-button');
            optionButtons.forEach((btn, idx) => {
                btn.classList.add('disabled');
                btn.onclick = null; // Remove click listener
                if (questionData.options[idx].isCorrect) {
                    btn.classList.add('correct'); // Highlight correct answer
                } else if (idx === selectedIndex && !selectedOption.isCorrect) {
                    btn.classList.add('incorrect'); // Highlight user's incorrect choice
                }
            });


            if (selectedOption.isCorrect) {
                currentPathScore++;
                feedbackTextEl.textContent = questionData.feedbackCorrect;
                feedbackTextEl.className = 'correct';
            } else {
                feedbackTextEl.textContent = questionData.feedbackIncorrect;
                feedbackTextEl.className = 'incorrect';
            }
            feedbackTextEl.classList.remove('hidden');

            currentQuestionIndex++;
            if (currentQuestionIndex < currentPathTotalQuestions) {
                nextStepButton.textContent = "Next Question";
            } else {
                nextStepButton.textContent = "View Path Conclusion";
            }
            nextStepButton.classList.remove('hidden');
        }

        nextStepButton.onclick = () => {
            const buttonText = nextStepButton.textContent;
            if (buttonText === "Next Question") {
                displayQuestion();
            } else if (buttonText === "View Path Conclusion") {
                displayPathConclusion();
            } else if (buttonText === "View Scenario Summary") {
                displayScenarioSummary();
            } else if (buttonText === "Back to Scenarios") {
                renderScenarioList();
            }
        };

        function displayPathConclusion() {
            questionAreaDiv.classList.add('hidden');
            pathConclusionAreaDiv.classList.remove('hidden');
            let conclusionMessage = `You answered ${currentPathScore} out of ${currentPathTotalQuestions} questions correctly in this path. `;
            conclusionMessage += currentScenario[currentPath].conclusion;
            pathConclusionTextEl.innerHTML = conclusionMessage.replace(/\n/g, '<br>'); // Use innerHTML for <br>

            nextStepButton.textContent = "View Scenario Summary";
        }

        function displayScenarioSummary() {
            pathConclusionAreaDiv.classList.add('hidden');
            scenarioSummaryAreaDiv.classList.remove('hidden');
            
            const summary = currentScenario.summary;
            let summaryHTML = `<h4>Key Takeaways:</h4><p>${summary.takeaways.replace(/\n/g, '<br>')}</p>`;
            summaryHTML += `<h4>Ethical Considerations:</h4><p>${summary.ethicalConsiderations.replace(/\n/g, '<br>')}</p>`;
            scenarioSummaryContentEl.innerHTML = summaryHTML;

            completedScenarioIDs.add(currentScenario.id);
            updateProgressTracker();
            nextStepButton.textContent = "Back to Scenarios";
        }

        // Initial load
        document.addEventListener('DOMContentLoaded', () => {
            renderScenarioList();
        });

    </script>
</body>
</html>
