
import React from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { Module } from '../types';
import AIPromptEngineeringPractice from '../components/modules/AIPromptEngineeringPractice';
import MedicalImagingExplorer from '../components/modules/MedicalImagingExplorer';
import HTMLModuleViewer from '../components/modules/HTMLModuleViewer';
import ModuleDetailPlaceholder from '../components/ModuleDetailPlaceholder'; // Default placeholder
import { ArrowLeftIcon } from '../components/icons/ModuleIcons';

interface ModulePageProps {
  modules: Module[];
}

const ModulePage: React.FC<ModulePageProps> = ({ modules }) => {
  const { moduleId } = useParams<{ moduleId: string }>();
  const module = modules.find(m => m.id === moduleId);

  if (!module) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-semibold text-neutral-700 mb-4">Module Not Found</h2>
        <p className="text-neutral-500 mb-6">The module you are looking for does not exist or may have been moved.</p>
        <Link to="/" className="text-primary-600 hover:text-primary-800 font-medium flex items-center justify-center">
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Modules
        </Link>
      </div>
    );
  }

  const renderInteractiveComponent = () => {
    switch (module.interactiveComponentKey) {
      case 'AIPromptPractice':
        return <AIPromptEngineeringPractice moduleTitle={module.title} />;
      case 'MedicalImagingExplorer':
        return <MedicalImagingExplorer moduleTitle={module.title} />;
      case 'HTMLModule':
        return module.htmlFileName ? (
          <HTMLModuleViewer
            moduleTitle={module.title}
            htmlFileName={module.htmlFileName}
            description="Interactive learning module with hands-on exercises and simulations"
          />
        ) : (
          <ModuleDetailPlaceholder title="HTML Module" content="HTML file not specified for this module." />
        );
      case 'InteractiveScenarioPlaceholder':
        return <ModuleDetailPlaceholder title="Interactive Scenario" content="This section will feature an interactive scenario. (Placeholder)" />;
      case 'MedicalAssistantSimulatorPlaceholder':
        return <ModuleDetailPlaceholder title="AI Medical Assistant Simulator" content="Engage with a simulated AI medical assistant. (Placeholder)" />;
      case 'DiagnosticAssistantPlaceholder':
         return <ModuleDetailPlaceholder title="Diagnostic Assistant Tool" content="Explore AI diagnostic algorithms. (Placeholder)" />;
      case 'SmartHealthSimulatorPlaceholder':
        return <ModuleDetailPlaceholder title="Smart Health Assistant Simulator" content="Conceptual overview of VR interaction with AI health tools. (Placeholder)" />;
      default:
        // If no specific interactive component, show general module content or a generic placeholder.
        // For now, this could also be handled by the main ModuleDetailPlaceholder structure below.
        return null;
    }
  };
  
  const InteractiveComponent = renderInteractiveComponent();

  return (
    <div className="bg-white p-6 sm:p-8 rounded-xl shadow-xl">
      <Link to="/" className="inline-flex items-center text-primary-600 hover:text-primary-800 mb-6 group">
        <ArrowLeftIcon className="h-5 w-5 mr-2 transform transition-transform duration-300 group-hover:-translate-x-1" />
        Back to Modules
      </Link>

      <div className="mb-8">
        <div className="flex items-center mb-2">
          {module.icon && <module.icon className="h-10 w-10 text-primary-600 mr-3" />}
          <h1 className="text-3xl lg:text-4xl font-bold text-primary-800">{module.title}</h1>
        </div>
        <span className={`text-sm font-medium px-2 py-1 rounded-full text-white ${
          module.learningTrack === 'Clinical Professionals' ? 'bg-sky-500' : 
          module.learningTrack === 'Engineering Professionals' ? 'bg-emerald-500' : 'bg-indigo-500'
        }`}>
          {module.learningTrack}
        </span>
      </div>

      <p className="text-lg text-neutral-700 mb-6">{module.longDescription}</p>

      {module.content?.overview && (
        <div className="mb-6 p-4 bg-primary-50 rounded-lg">
          <h2 className="text-xl font-semibold text-primary-700 mb-2">Module Overview</h2>
          <p className="text-neutral-600">{module.content.overview}</p>
        </div>
      )}
      
      {InteractiveComponent ? (
        <div className="my-8 p-4 border-t border-neutral-200">
          <h2 className="text-2xl font-semibold text-primary-700 mb-4">Interactive Session</h2>
          {InteractiveComponent}
        </div>
      ) : module.interactiveComponentKey ? (
         <div className="my-8 p-4 border-t border-neutral-200">
            <h2 className="text-2xl font-semibold text-primary-700 mb-4">Interactive Session</h2>
            <ModuleDetailPlaceholder title="Coming Soon" content={`Interactive content for "${module.title}" is under development.`} />
        </div>
      ) : null}


      {module.content?.sections && module.content.sections.length > 0 && (
        <div className="mb-6">
          <h2 className="text-2xl font-semibold text-primary-700 mb-3">Key Sections</h2>
          <div className="space-y-4">
            {module.content.sections.map((section, index) => (
              <details key={index} className="bg-neutral-50 p-4 rounded-lg shadow-sm">
                <summary className="font-semibold text-neutral-700 cursor-pointer hover:text-primary-600">{section.title}</summary>
                <p className="mt-2 text-neutral-600">{section.content}</p>
                {section.subSections && section.subSections.map((sub, subIdx) => (
                     <div key={subIdx} className="ml-4 mt-2 p-3 bg-neutral-100 rounded">
                        <h4 className="font-medium text-neutral-700">{sub.title}</h4>
                        <p className="text-sm text-neutral-600">{sub.content}</p>
                     </div>
                ))}
              </details>
            ))}
          </div>
        </div>
      )}

      {module.content?.caseStudies && module.content.caseStudies.length > 0 && (
         <div className="mb-6">
          <h2 className="text-2xl font-semibold text-primary-700 mb-3">Case Studies</h2>
           {module.content.caseStudies.map((study, index) => (
             <div key={index} className="mb-4 p-4 bg-secondary-50 rounded-lg shadow-sm">
               <h3 className="text-lg font-semibold text-secondary-700">{study.title}</h3>
               <p className="text-neutral-600 my-1">{study.description}</p>
               <p className="text-sm text-neutral-500"><strong>Outcome:</strong> {study.outcome}</p>
             </div>
           ))}
         </div>
      )}
      
      <div className="mt-8 pt-6 border-t border-neutral-200">
        <h3 className="text-xl font-semibold text-neutral-700 mb-2">Tags</h3>
        <div className="flex flex-wrap gap-2">
          {module.tags.map(tag => (
            <span key={tag} className="bg-neutral-200 text-neutral-700 px-3 py-1 rounded-full text-sm">
              {tag}
            </span>
          ))}
        </div>
      </div>

      <div className="mt-8 text-center">
        <button className="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
          Mark as Complete (Placeholder)
        </button>
      </div>
       {module.content?.assessment && (
        <div className="mt-8 pt-6 border-t border-neutral-200">
            <h2 className="text-2xl font-semibold text-primary-700 mb-4">Assessment (Placeholder)</h2>
            <p className="text-neutral-600">An assessment with {module.content.assessment.questions.length} questions will be available here.</p>
            <button className="mt-4 bg-secondary-500 hover:bg-secondary-600 text-white font-semibold py-2 px-4 rounded-lg">
                Start Quiz (Placeholder)
            </button>
        </div>
      )}

    </div>
  );
};

export default ModulePage;
