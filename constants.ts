
import { Module, LearningTrack } from './types';
import { AcademicCapIcon, BriefcaseIcon, LightBulbIcon, CogIcon, StethoscopeIcon, CpuChipIcon, ChatBubbleLeftRightIcon,CommandLineIcon, UsersIcon, BeakerIcon, MagnifyingGlassIcon, SparklesIcon, PuzzlePieceIcon, QuestionMarkCircleIcon, PresentationChartLineIcon, CameraIcon, ExternalLinkIcon } from './components/icons/ModuleIcons';

export const modulesData: Module[] = [
  {
    id: 'ai-augmenting-doctors',
    title: 'AI Augmenting Doctors',
    description: 'Explore how AI supports diagnostics, treatment planning, and clinical workflows for medical professionals.',
    longDescription: 'This module delves into the practical applications of AI in enhancing physicians\' capabilities. Topics include AI-driven diagnostic tools, personalized treatment recommendations, and optimization of clinical workflows to improve patient outcomes and reduce clinician burnout.',
    learningTrack: LearningTrack.CLINICAL,
    icon: StethoscopeIcon,
    tags: ['Diagnostics', 'Clinical Workflow', 'Decision Support'],
    content: {
      overview: 'Understanding the synergy between AI and medical practitioners.',
      sections: [
        { title: 'AI in Diagnostics', content: 'Examples: image analysis (radiology, pathology), early disease detection.' },
        { title: 'AI in Treatment Planning', content: 'Personalized medicine, predicting treatment responses.' },
        { title: 'AI in Clinical Workflow Optimization', content: 'Automating administrative tasks, patient monitoring.' },
      ],
      caseStudies: [
        { title: 'Case Study: AI in Radiology', description: 'An AI algorithm used to detect early signs of cancer in medical images.', outcome: 'Improved detection rates and faster diagnosis.'}
      ],
    },
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'AI Augmenting Doctors.html'
  },
  {
    id: 'ai-medical-device-maintenance',
    title: 'AI in Medical Device Maintenance',
    description: 'Learn about preventive and predictive AI for maintaining biomedical equipment efficiently.',
    longDescription: 'Focuses on AI-powered strategies for ensuring the reliability and longevity of medical devices. Covers predictive maintenance algorithms, sensor-based monitoring, and IoT integration for proactive servicing, minimizing downtime and operational costs.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: CogIcon,
    tags: ['Predictive Maintenance', 'Biomedical Equipment', 'IoT'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'AI in Medical Device Maintenance.html'
  },
  {
    id: 'ai-medical-engineering',
    title: 'AI in Medical Engineering',
    description: 'Discover AI applications in biomedical device design, robotics, and advanced simulations.',
    longDescription: 'This module explores the cutting-edge use of AI in the design and development of new medical technologies. Includes AI in robotic surgery, development of smart prosthetics, and computational simulations for device testing and validation.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: CpuChipIcon,
    tags: ['Device Design', 'Robotics', 'Simulations', 'Bio-signal Processing'],
  },
  {
    id: 'ai-medicine-case-studies',
    title: 'AI in Medicine: Real-World Case Studies',
    description: 'In-depth case studies on AI-human synergy in various hospital settings and specialties.',
    longDescription: 'Examines successful implementations of AI in healthcare, highlighting the collaboration between AI systems and medical teams. Provides insights into challenges, ethical considerations, and best practices from real-world scenarios.',
    learningTrack: LearningTrack.BOTH,
    icon: PresentationChartLineIcon,
    tags: ['Case Studies', 'AI Implementation', 'Healthcare Outcomes'],
  },
  {
    id: 'ai-interactive-scenarios',
    title: 'AI in Medicine: Interactive Scenarios',
    description: 'Engage in scenario-based training using simulated patient-AI interactions and decision-making.',
    longDescription: 'Offers hands-on experience through interactive simulations where learners navigate clinical scenarios involving AI tools. Develop critical thinking and decision-making skills in AI-assisted patient care environments.',
    learningTrack: LearningTrack.CLINICAL,
    icon: PuzzlePieceIcon,
    tags: ['Simulation', 'Scenario-Based Learning', 'Decision Making'],
    interactiveComponentKey: 'InteractiveScenarioPlaceholder',
  },
  {
    id: 'ai-medicine-your-perspective',
    title: 'AI in Medicine: Your Perspective',
    description: 'Engage in reflective learning and critical analysis of AI\'s role and impact in healthcare.',
    longDescription: 'Encourages learners to critically evaluate the ethical, social, and practical implications of AI in medicine. Fosters discussion on AI bias, patient privacy, and the future role of healthcare professionals in an AI-driven landscape.',
    learningTrack: LearningTrack.BOTH,
    icon: QuestionMarkCircleIcon,
    tags: ['Ethics', 'Critical Analysis', 'Future of Healthcare'],
  },
  {
    id: 'ai-medical-assistant-simulator',
    title: 'AI Medical Assistant Simulator',
    description: 'Experience virtual assistant use in triage, scheduling, and clinical decision support.',
    longDescription: 'A simulated environment to interact with an AI medical assistant. Practice using AI tools for patient triage, appointment scheduling, and accessing information for decision support in a controlled setting.',
    learningTrack: LearningTrack.CLINICAL,
    icon: UsersIcon,
    tags: ['Virtual Assistant', 'Triage', 'Scheduling', 'Simulation'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'AI Medical Assistant Simulator.html'
  },
  {
    id: 'ai-prompt-engineering-practice',
    title: 'AI Prompt Engineering Practice (BME Assistant)',
    description: 'Train on crafting effective prompts for generative AI models in biomedical contexts.',
    longDescription: 'Develop skills in formulating precise and effective prompts to interact with generative AI models relevant to biomedical engineering and clinical queries. Learn to refine prompts for optimal information retrieval and content generation.',
    learningTrack: LearningTrack.BOTH,
    icon: CommandLineIcon,
    tags: ['Generative AI', 'Prompt Engineering', 'BME'],
    interactiveComponentKey: 'AIPromptPractice',
  },
  {
    id: 'ai-futures-in-medicine',
    title: 'AI’s Futures in Medicine',
    description: 'Explore forecasts, emerging innovations, and transformative trends in AI healthcare applications.',
    longDescription: 'Looks ahead at the potential of AI to reshape medicine. Discusses upcoming technologies, research frontiers, and the long-term vision for AI in diagnostics, therapeutics, and public health.',
    learningTrack: LearningTrack.BOTH,
    icon: LightBulbIcon,
    tags: ['Innovation', 'Future Trends', 'Healthcare Transformation'],
  },
  {
    id: 'biomedical-engineering-assistant-module',
    title: 'Biomedical Engineering Assistant',
    description: 'Tutorials on integrating AI with biotechnologies, smart sensors, and wearable devices.',
    longDescription: 'Provides practical guidance for biomedical engineers on leveraging AI in conjunction with various biotechnologies. Covers data analysis from wearables, AI in genetic engineering, and smart sensor integration for health monitoring.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: BeakerIcon,
    tags: ['Biotechnology', 'Wearables', 'Sensors', 'Data Analysis'],
  },
  {
    id: 'diagnostic-assistant-tool',
    title: 'Diagnostic Assistant Insights',
    description: 'Hands-on tools and insights for understanding AI-powered diagnostic algorithms and their interpretation.',
    longDescription: 'Offers an interactive exploration of how AI algorithms assist in medical diagnosis. Understand the principles behind common diagnostic AI, interpret their outputs, and learn about their limitations and strengths.',
    learningTrack: LearningTrack.CLINICAL,
    icon: MagnifyingGlassIcon,
    tags: ['Diagnostics', 'AI Algorithms', 'Interpretation'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'Diagnostic Assistant.html'
  },
  {
    id: 'generative-ai-chatbot-bme',
    title: 'Generative AI Chatbot for BME',
    description: 'Learn principles of building custom chatbots for hospital, lab, and research workflows.',
    longDescription: 'Guides learners through the fundamentals of designing and implementing generative AI-based chatbots tailored for biomedical engineering applications, such as information retrieval in labs or patient support in hospitals.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: ChatBubbleLeftRightIcon,
    tags: ['Chatbots', 'Generative AI', 'Workflow Automation'],
    interactiveComponentKey: 'AIPromptPractice', // Can reuse for a similar interaction pattern
  },
  {
    id: 'medical-equipment-ai-assistant',
    title: 'Medical Equipment AI Assistant',
    description: 'Explore intelligent agents for equipment monitoring, automated feedback, and operational efficiency.',
    longDescription: 'Focuses on AI systems designed to assist with the management and operation of medical equipment. Covers real-time monitoring, fault detection, automated reporting, and optimizing usage patterns for enhanced efficiency.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: SparklesIcon,
    tags: ['Equipment Monitoring', 'Intelligent Agents', 'Operational Efficiency'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'Medical Equipment AI Assistant.html'
  },
  {
    id: 'manual-vs-ai-equipment-assistant',
    title: 'AI vs. Manual Equipment Processes',
    description: 'Comparative analysis of AI-supported versus traditional manual processes in medical equipment management.',
    longDescription: 'This module provides a detailed comparison between AI-driven approaches and conventional manual methods for managing and maintaining medical equipment. Analyze benefits, challenges, cost-effectiveness, and impact on patient safety.',
    learningTrack: LearningTrack.BOTH,
    icon: BriefcaseIcon, // Generic business/process icon
    tags: ['Process Comparison', 'Efficiency', 'Cost-Benefit Analysis'],
  },
  {
    id: 'simple-bme-assistant',
    title: 'Fundamentals of AI for BME',
    description: 'A starter module for entry-level biomedical engineers new to AI concepts and applications.',
    longDescription: 'An introductory course designed for biomedical engineers beginning their journey into AI. Covers fundamental AI concepts, terminology, and basic applications relevant to the BME field, setting a foundation for more advanced topics.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: AcademicCapIcon,
    tags: ['入门级', 'AI Fundamentals', 'Biomedical Engineering Basics'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'Simple Biomedical Engineering Assistant.html'
  },
  {
    id: 'smart-health-assistant-simulator',
    title: 'Smart Health Assistant Simulator (VR Prep)',
    description: 'Conceptual overview of virtual reality-based interaction with AI health tools and future possibilities.',
    longDescription: 'Explores the concept and potential of using Virtual Reality (VR) to interact with AI-powered smart health assistants. This module is preparatory, discussing design considerations and use cases for future VR simulations.',
    learningTrack: LearningTrack.BOTH,
    icon: UsersIcon, // Reusing icon
    tags: ['VR', 'Simulation', 'Future Tech', 'User Interface'],
    interactiveComponentKey: 'SmartHealthSimulatorPlaceholder',
  },
  {
    id: 'doctor-ai-future',
    title: 'The Doctor & AI: Navigating the Future',
    description: 'Discusses ethics, collaboration dynamics, and the evolving relationship between doctors and AI.',
    longDescription: 'A forward-looking module that addresses the critical aspects of the human-AI partnership in medicine. Explores ethical dilemmas, strategies for effective collaboration, regulatory landscapes, and the changing roles of medical professionals.',
    learningTrack: LearningTrack.CLINICAL,
    icon: UsersIcon, // Reusing icon
    tags: ['Ethics', 'Collaboration', 'Future of Medicine', 'Professional Development'],
  },
  {
    id: 'medical-imaging-modalities',
    title: 'Medical Imaging Modalities & AI',
    description: 'Comprehensive exploration of medical imaging technologies and AI-enhanced image analysis techniques.',
    longDescription: 'This module provides an in-depth understanding of various medical imaging modalities including X-ray, CT, MRI, ultrasound, PET, and nuclear medicine. Learn how AI is revolutionizing image acquisition, processing, and interpretation to improve diagnostic accuracy and patient outcomes.',
    learningTrack: LearningTrack.BOTH,
    icon: CameraIcon,
    tags: ['Medical Imaging', 'Radiology', 'AI Image Analysis', 'Diagnostic Imaging', 'Computer Vision'],
    content: {
      overview: 'Understanding medical imaging technologies and their AI-enhanced applications in modern healthcare.',
      sections: [
        {
          title: 'Conventional Radiography (X-ray)',
          content: 'Digital radiography systems, image enhancement techniques, and AI-powered fracture detection algorithms.',
          subSections: [
            { title: 'Digital X-ray Systems', content: 'Direct and indirect conversion detectors, image processing workflows.' },
            { title: 'AI in Chest X-rays', content: 'Automated detection of pneumonia, tuberculosis, and lung nodules.' },
            { title: 'Bone Fracture Detection', content: 'Machine learning models for automated fracture identification and classification.' }
          ]
        },
        {
          title: 'Computed Tomography (CT)',
          content: 'CT imaging principles, reconstruction algorithms, and AI applications in cross-sectional imaging.',
          subSections: [
            { title: 'CT Physics & Reconstruction', content: 'Filtered back projection, iterative reconstruction, and dose optimization.' },
            { title: 'AI in CT Imaging', content: 'Automated organ segmentation, lesion detection, and quantitative analysis.' },
            { title: 'Low-dose CT Protocols', content: 'AI-powered noise reduction and image enhancement techniques.' }
          ]
        },
        {
          title: 'Magnetic Resonance Imaging (MRI)',
          content: 'MRI physics, pulse sequences, and AI-enhanced image acquisition and analysis.',
          subSections: [
            { title: 'MRI Physics & Sequences', content: 'T1, T2, FLAIR, DWI, and advanced sequences like DTI and fMRI.' },
            { title: 'AI in MRI', content: 'Automated brain segmentation, tumor detection, and quantitative imaging biomarkers.' },
            { title: 'Accelerated MRI', content: 'Compressed sensing and deep learning for faster image acquisition.' }
          ]
        },
        {
          title: 'Ultrasound Imaging',
          content: 'Ultrasound physics, Doppler techniques, and AI applications in real-time imaging.',
          subSections: [
            { title: 'Ultrasound Physics', content: 'Acoustic properties, beam forming, and image optimization techniques.' },
            { title: 'AI in Ultrasound', content: 'Automated measurements, organ recognition, and pathology detection.' },
            { title: 'Point-of-Care Ultrasound', content: 'AI-guided imaging protocols and automated image interpretation.' }
          ]
        },
        {
          title: 'Nuclear Medicine & PET',
          content: 'Radiopharmaceuticals, SPECT/PET imaging, and AI in molecular imaging.',
          subSections: [
            { title: 'Nuclear Medicine Principles', content: 'Radiotracer kinetics, gamma cameras, and image reconstruction.' },
            { title: 'PET/CT Imaging', content: 'Metabolic imaging, attenuation correction, and quantitative analysis.' },
            { title: 'AI in Nuclear Medicine', content: 'Automated lesion detection, SUV quantification, and prognostic modeling.' }
          ]
        },
        {
          title: 'AI Image Analysis Techniques',
          content: 'Deep learning architectures, computer vision algorithms, and clinical implementation.',
          subSections: [
            { title: 'Convolutional Neural Networks', content: 'CNN architectures for medical image classification and segmentation.' },
            { title: 'Image Preprocessing', content: 'Normalization, augmentation, and quality assessment techniques.' },
            { title: 'Clinical Validation', content: 'Performance metrics, regulatory considerations, and deployment strategies.' }
          ]
        }
      ],
      caseStudies: [
        {
          title: 'AI-Powered Mammography Screening',
          description: 'Implementation of deep learning algorithms for breast cancer detection in mammography screening programs.',
          outcome: 'Reduced false positive rates by 25% and improved early-stage cancer detection by 15%.'
        },
        {
          title: 'Automated Brain Tumor Segmentation',
          description: 'Multi-modal MRI analysis using AI for precise tumor boundary delineation in surgical planning.',
          outcome: 'Achieved 95% accuracy in tumor segmentation, reducing radiologist review time by 60%.'
        },
        {
          title: 'COVID-19 Chest CT Analysis',
          description: 'Rapid deployment of AI models for COVID-19 pneumonia detection and severity assessment.',
          outcome: 'Enabled faster triage decisions and reduced radiologist workload during pandemic surge.'
        },
        {
          title: 'Retinal Disease Detection',
          description: 'AI-based fundus photography analysis for diabetic retinopathy and age-related macular degeneration.',
          outcome: 'Achieved sensitivity >90% for sight-threatening conditions, enabling population-scale screening.'
        }
      ]
    },
    interactiveComponentKey: 'MedicalImagingExplorer',
  },
  {
    id: 'ai-interactive-scenarios',
    title: 'AI in Medicine: Interactive Scenarios',
    description: 'Engage in scenario-based training using simulated patient-AI interactions and decision-making.',
    longDescription: 'Offers hands-on experience through interactive simulations where learners navigate clinical scenarios involving AI tools. Develop critical thinking and decision-making skills in AI-assisted patient care environments.',
    learningTrack: LearningTrack.CLINICAL,
    icon: PuzzlePieceIcon,
    tags: ['Simulation', 'Scenario-Based Learning', 'Decision Making'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'AI in Medicine Interactive Scenarios.html'
  },
  {
    id: 'ai-prompt-engineering-bme',
    title: 'AI Prompt Engineering Practice (BME)',
    description: 'Practice crafting effective prompts for AI assistants in biomedical engineering contexts.',
    longDescription: 'Develop skills in formulating precise and effective prompts to interact with generative AI models relevant to biomedical engineering and clinical queries. Learn to refine prompts for optimal information retrieval and content generation.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: CommandLineIcon,
    tags: ['Prompt Engineering', 'AI Communication', 'BME Applications'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'AI Prompt Engineering Practice  BME Assistant.html'
  },
  {
    id: 'biomedical-engineering-assistant',
    title: 'Biomedical Engineering Assistant',
    description: 'Comprehensive AI assistant for biomedical engineering tasks and troubleshooting.',
    longDescription: 'Provides practical guidance for biomedical engineers on leveraging AI in conjunction with various biotechnologies. Covers data analysis from wearables, AI in genetic engineering, and smart sensor integration for health monitoring.',
    learningTrack: LearningTrack.ENGINEERING,
    icon: BeakerIcon,
    tags: ['BME Assistant', 'Troubleshooting', 'Technical Support'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'Biomedical Engineering Assistant.html'
  },
  {
    id: 'smart-health-assistant',
    title: 'Smart Health Assistant Simulator',
    description: 'Experience next-generation AI health assistants and their potential applications.',
    longDescription: 'Explores the concept and potential of using advanced AI to create smart health assistants. This module discusses design considerations, use cases, and future possibilities for AI-powered health monitoring and support systems.',
    learningTrack: LearningTrack.BOTH,
    icon: SparklesIcon,
    tags: ['Smart Health', 'AI Assistant', 'Future Technology'],
    interactiveComponentKey: 'HTMLModule',
    htmlFileName: 'Smart Health Assistant Simulator.html'
  }
];
