<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Prompt Engineering Practice: BME Assistant</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f9;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            flex-grow: 1;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 15px;
        }

        header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        header p {
            font-size: 0.95em;
            color: #555;
        }

        .app-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fdfdfd;
        }

        label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #4a90e2;
        }

        select, textarea, button {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 1em;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-weight: bold;
        }

        button:hover {
            background-color: #357abd;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .scenario-display {
            background-color: #e9f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            border-left: 4px solid #4a90e2;
        }
        .scenario-display p {
            margin:0;
            font-style: italic;
            color: #333;
        }

        .output-area {
            margin-top: 20px;
        }

        .output-box {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            min-height: 50px;
        }

        .ai-response {
            background-color: #e6fffa;
            border: 1px solid #00bfa5; /* Teal border */
        }
        .ai-response h4 { margin-top: 0; color: #00796b; } /* Darker teal */

        .prompt-feedback {
            background-color: #fff3e0; /* Light orange/yellow */
            border: 1px solid #ff9800; /* Orange border */
        }
        .prompt-feedback h4 { margin-top: 0; color: #e65100; } /* Darker orange */
        .prompt-feedback.good { background-color: #e8f5e9; border-color: #4caf50; }
        .prompt-feedback.good h4 { color: #2e7d32; }
        .prompt-feedback.okay { background-color: #fffde7; border-color: #ffeb3b; }
        .prompt-feedback.okay h4 { color: #f9a825; }
        .prompt-feedback.poor { background-color: #ffebee; border-color: #f44336; }
        .prompt-feedback.poor h4 { color: #c62828; }

        footer {
            text-align: center;
            padding: 15px;
            background-color: #2c3e50;
            color: #ecf0f1;
            font-size: 0.9em;
        }
        footer p { margin: 0; }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                width: 95%;
                padding: 15px;
            }
            header h1 {
                font-size: 1.5em;
            }
            textarea {
                min-height: 80px;
            }
        }

    </style>
</head>
<body>

    <header>
        <h1>AI Prompt Engineering Gym 🏋️</h1>
        <p>Practice crafting effective prompts for a (simulated) AI assistant used by Biomedical Engineers!</p>
    </header>

    <div class="container">
        <div class="app-section">
            <label for="scenario-selector">1. Choose a Scenario:</label>
            <select id="scenario-selector"></select>
            <div class="scenario-display">
                <h4>Current Scenario:</h4>
                <p id="scenario-text">Please select a scenario from the dropdown above.</p>
            </div>
        </div>

        <div class="app-section">
            <label for="prompt-input">2. Write Your Prompt to the AI Assistant:</label>
            <textarea id="prompt-input" placeholder="e.g., 'Provide troubleshooting steps for...' or 'List common causes of...'"></textarea>
        </div>

        <button id="generate-button">3. Generate AI Response & Get Feedback</button>

        <div class="output-area">
            <div id="ai-response-area" class="output-box ai-response" style="display: none;">
                <h4>🤖 AI Assistant's Simulated Response:</h4>
                <p id="ai-response-text"></p>
            </div>
            <div id="feedback-area" class="output-box prompt-feedback" style="display: none;">
                <h4>💡 Feedback on Your Prompt:</h4>
                <p id="feedback-text"></p>
            </div>
        </div>
         <div class="app-section" style="margin-top:30px; background-color: #eef; border-left: 3px solid #66c;">
            <h4>How This Works:</h4>
            <p style="font-size:0.9em;">This app simulates an AI assistant to help you learn prompt engineering.
            When you "Generate Response":
            <ol style="font-size:0.9em; margin-top:5px; padding-left: 20px;">
                <li>Your prompt is evaluated based on pre-defined rules for the selected scenario. This gives you <strong>deterministic feedback</strong> on your prompt's quality.</li>
                <li>A simulated AI response is then chosen. The <em>type</em> of response (helpful, unhelpful, etc.) is influenced by your prompt's evaluated quality.</li>
            </ol>
            The goal is to refine your prompts to consistently get "Good" feedback and helpful AI responses!
            </p>
        </div>
    </div>

    <footer>
        <p>Prompt Engineering Practice App - Built for Learning</p>
    </footer>

    <script>
        const scenariosData = {
            "icu_beeping": {
                text: "A vital signs monitor in the ICU is beeping constantly.",
                aiResponses: {
                    good: [
                        "Okay, let's troubleshoot the constant beeping. First, check if the alarm limits (e.g., for heart rate, SpO2) are set appropriately for this patient. Second, ensure all sensor connections (ECG leads, SpO2 probe, BP cuff) are secure and making good contact. Can you tell me if the monitor displays a specific message or which parameter is alarming?",
                        "Constant beeping indicates an active alarm. To help, I need more information. Please look at the monitor: Is there a specific alarm message displayed (e.g., 'HR HIGH', 'SPO2 LOW', 'LEAD OFF')? Knowing the specific alarm will help us narrow down the cause."
                    ],
                    okay: [
                        "A beeping monitor usually means something is wrong. Try checking the connections.",
                        "You should identify why it's beeping. Look at the screen. What does it say?"
                    ],
                    poor: [
                        "I am an AI assistant and cannot physically inspect the monitor. Please call a technician.",
                        "Beeping is normal for monitors sometimes. It might stop on its own.",
                        "Ensure the patient is comfortable. That's the most important thing."
                    ]
                },
                feedbackRules: [
                    {
                        condition: (p) => p.length < 10 || p.toLowerCase() === "help" || p.toLowerCase() === "fix it",
                        feedbackText: "Your prompt is quite short or too demanding without specifics. Try to include key details from the scenario (like 'monitor beeping in ICU') and what you want the AI to do (e.g., 'give troubleshooting steps', 'list possible causes').",
                        promptQuality: "poor"
                    },
                    {
                        condition: (p) => (p.toLowerCase().includes("what to do") || p.toLowerCase().includes("what now")) && p.toLowerCase().includes("beeping"),
                        feedbackText: "You've indicated you need a solution for the beeping. To get a better AI response, try phrasing it as a request for specific information, like 'What are common causes for a beeping ICU monitor?' or 'Guide me through troubleshooting a beeping monitor.'",
                        promptQuality: "okay"
                    },
                    {
                        condition: (p) => (p.toLowerCase().includes("troubleshoot") || p.toLowerCase().includes("steps") || p.toLowerCase().includes("guide") || p.toLowerCase().includes("causes")) && (p.toLowerCase().includes("beeping") || p.toLowerCase().includes("alarm")) && (p.toLowerCase().includes("monitor") || p.toLowerCase().includes("icu")),
                        feedbackText: "Excellent prompt! You've clearly stated the problem, included context, and asked for specific actions. This helps the AI provide a targeted and useful response.",
                        promptQuality: "good"
                    },
                    {
                        condition: (p) => true,
                        feedbackText: "Consider rephrasing. A good prompt clearly states the problem from the scenario and tells the AI what kind of help you need (e.g., 'list steps', 'explain X'). Include keywords like 'beeping', 'monitor', 'ICU'.",
                        promptQuality: "poor"
                    }
                ]
            },
            "low_spo2_patient_fine": {
                text: "A patient's SpO2 reading is consistently low (e.g., 85%), but the patient appears clinically stable and not in distress.",
                aiResponses: {
                    good: [
                        "This situation suggests a potential issue with the SpO2 measurement itself rather than acute hypoxia. Here are steps to verify: 1. Check probe placement – is it secure and appropriate for the patient (finger, toe, ear)? 2. Try a different probe or a different finger/site. 3. Assess for factors affecting readings: poor perfusion, nail polish, ambient light, patient movement. What type of probe are you using?",
                        "A discrepancy between SpO2 readings and clinical presentation warrants careful investigation. Consider these possibilities: Is the probe correctly sized and positioned? Is there any interference like excessive motion, dark nail polish, or bright ambient light? You might also want to try an alternate site or a different oximeter if available. If readings remain low despite these checks and the patient truly appears fine, advise clinical staff to consider cross-verification with an arterial blood gas if clinically indicated and ordered."
                    ],
                    okay: [
                        "Low SpO2 can be serious. Maybe the probe is loose or the wrong size. Check that first.",
                        "If the patient is fine, the monitor might be wrong. Check the SpO2 sensor and its connection."
                    ],
                    poor: [
                        "Immediately inform the doctor to increase the oxygen supply to the patient.",
                        "The monitor is probably broken. You should request a replacement unit for this patient.",
                        "Just document the SpO2 reading and continue monitoring. If the patient looks fine, they are fine."
                    ]
                },
                feedbackRules: [
                    {
                        condition: (p) => p.length < 15 || p.toLowerCase().includes("what now"),
                        feedbackText: "Your prompt is a bit brief. Include key details like 'low SpO2', 'patient fine', and ask for specific help like 'troubleshooting steps for SpO2 reading' or 'possible causes for false low SpO2'.",
                        promptQuality: "poor"
                    },
                    {
                        condition: (p) => p.toLowerCase().includes("spo2 low") && (p.toLowerCase().includes("check") || p.toLowerCase().includes("verify")) && !p.toLowerCase().includes("patient fine"),
                        feedbackText: "Good start! You're focusing on the SpO2. To improve, it's crucial to also mention that the patient appears fine, as this changes the troubleshooting approach significantly. Then, ask for a systematic approach or common causes of inaccurate readings.",
                        promptQuality: "okay"
                    },
                    {
                        condition: (p) => (p.toLowerCase().includes("troubleshoot") || p.toLowerCase().includes("steps") || p.toLowerCase().includes("causes") || p.toLowerCase().includes("investigate") || p.toLowerCase().includes("why")) && p.toLowerCase().includes("spo2") && (p.toLowerCase().includes("low") || p.toLowerCase().includes("reading") || p.toLowerCase().includes("inaccurate")) && (p.toLowerCase().includes("patient fine") || p.toLowerCase().includes("stable") || p.toLowerCase().includes("no distress") || p.toLowerCase().includes("appears fine")),
                        feedbackText: "Excellent! You've provided full context (low SpO2, patient stable) and asked for specific actions. This allows the AI to give a highly relevant and helpful response.",
                        promptQuality: "good"
                    },
                    {
                        condition: (p) => true,
                        feedbackText: "Try to be more specific. Mention 'low SpO2', 'patient appears fine', and what you want the AI to do (e.g., 'list troubleshooting steps for inaccurate SpO2 readings').",
                        promptQuality: "poor"
                    }
                ]
            },
            "ecg_artifact": {
                text: "The ECG waveform on the monitor is showing a lot of artifact, making it difficult to interpret the rhythm.",
                aiResponses: {
                    good: [
                        "ECG artifact can be caused by several factors. Let's go through a checklist: 1. Patient movement: Is the patient restless or shivering? 2. Electrode contact: Are the electrodes fresh, properly adhered, and not dried out? Check skin prep. 3. Lead wires: Are they secure at both electrode and monitor ends? Are they tangled or near sources of electrical interference? 4. Electrical interference: Are other devices (e.g., IV pumps, cell phones) nearby? Trying a different ECG cable if available can also help.",
                        "Artifact on an ECG can obscure the true rhythm. Key areas to check are: Patient factors (movement, muscle tremor), Electrode application (good skin contact, sufficient gel, correct placement, good skin prep), Lead wire integrity (no breaks, secure connections), and External interference (electrical devices). Could you describe the type of artifact if possible (e.g., baseline wander, 60Hz interference, muscle tremor)? This might give more clues."
                    ],
                    okay: [
                        "Check the patient's electrodes. They might be loose or dry. Also, see if the patient is moving a lot.",
                        "Artifact is common with ECGs. Try to get the patient to stay still and re-check lead connections."
                    ],
                    poor: [
                        "Ignore the artifact and focus on other vital signs like SpO2 and NIBP.",
                        "This is likely a software issue with the monitor. You should try restarting the monitor.",
                        "You probably need a cardiologist to interpret this ECG if there's too much artifact."
                    ]
                },
                feedbackRules: [
                    {
                        condition: (p) => p.length < 10 || p.toLowerCase() === "ecg problem",
                        feedbackText: "Your prompt is too short. Be sure to mention 'ECG artifact' and what kind of help you need, for example, 'steps to reduce ECG artifact' or 'common causes of ECG artifact'.",
                        promptQuality: "poor"
                    },
                    {
                        condition: (p) => p.toLowerCase().includes("ecg artifact") && (p.toLowerCase().includes("fix") || p.toLowerCase().includes("solve") || p.toLowerCase().includes("what do")),
                        feedbackText: "You've identified the problem as 'ECG artifact'. To get more targeted help, ask for specific actions like 'list common causes of ECG artifact' or 'provide a troubleshooting checklist for ECG artifact'.",
                        promptQuality: "okay"
                    },
                    {
                        condition: (p) => (p.toLowerCase().includes("reduce") || p.toLowerCase().includes("troubleshoot") || p.toLowerCase().includes("steps") || p.toLowerCase().includes("causes") || p.toLowerCase().includes("minimize")) && p.toLowerCase().includes("ecg") && p.toLowerCase().includes("artifact"),
                        feedbackText: "Great prompt! You've clearly stated the issue ('ECG artifact') and what you need the AI to do (e.g., 'troubleshoot', 'list causes'). This will help the AI give practical advice.",
                        promptQuality: "good"
                    },
                    {
                        condition: (p) => true,
                        feedbackText: "Make your prompt more specific. Include 'ECG artifact' and ask for actionable information like 'troubleshooting steps for ECG artifact' or 'how to improve ECG signal quality'.",
                        promptQuality: "poor"
                    }
                ]
            },
            "nibp_calibration": {
                text: "I need to calibrate the non-invasive blood pressure (NIBP) module on a specific monitor (e.g., Philips IntelliVue MX700), but I'm unsure of the exact steps.",
                aiResponses: {
                    good: [
                        "Calibrating an NIBP module typically involves accessing the service mode and using a calibrated manometer and a cuff/volume tank. For a Philips IntelliVue MX700, you'd generally: 1. Gather equipment: calibrated reference manometer, appropriate tubing, and possibly a rigid volume chamber. 2. Enter service/maintenance mode on the monitor (consult the service manual for exact key presses/menu navigation). 3. Navigate to NIBP calibration section. 4. Follow on-screen prompts to connect the manometer and perform zeroing and pressure point checks (e.g., at 50, 150, 250 mmHg). Always refer to the specific service manual for the MX700 for precise instructions, safety precautions, and pass/fail criteria.",
                        "NIBP calibration requires precision. For the IntelliVue MX700, the general procedure is: 1. Ensure you have the service manual, a certified reference manometer, and appropriate connectors/hoses. 2. Access the monitor's calibration menu (this is usually in a protected service mode). 3. The procedure will likely involve a leak test first. 4. Then, perform the pressure calibration at various set points as prompted by the monitor, comparing the monitor's readings against your reference manometer. Do you have the service manual handy for the MX700? It will contain the definitive steps and any specific warnings or prerequisites."
                    ],
                    okay: [
                        "NIBP calibration is important for accuracy. You'll need a calibrated manometer and should look in the service manual for the Philips IntelliVue MX700 for detailed instructions.",
                        "To calibrate NIBP on the MX700, you generally connect a pressure source and follow the monitor's on-screen instructions in the service mode. Make sure your reference is accurate."
                    ],
                    poor: [
                        "Just use the auto-calibration feature if the Philips IntelliVue MX700 has one. That should be enough.",
                        "You probably don't need to calibrate it unless it's giving obviously wrong readings or is very old.",
                        "I cannot provide specific calibration steps for proprietary medical equipment due to safety and liability concerns. Contact Philips service."
                    ]
                },
                feedbackRules: [
                    {
                        condition: (p) => p.length < 20 || p.toLowerCase().includes("how to calibrate nibp") && !p.toLowerCase().includes("philips"),
                        feedbackText: "Your prompt is a bit general. While you've mentioned NIBP calibration, adding the monitor model ('Philips IntelliVue MX700' from the scenario) and specifically requesting 'step-by-step instructions' or 'procedure' will be more effective.",
                        promptQuality: "poor"
                    },
                    {
                        condition: (p) => p.toLowerCase().includes("calibrate nibp") && (p.toLowerCase().includes("steps") || p.toLowerCase().includes("procedure")) && !p.toLowerCase().includes("philips intellivue mx700") && !p.toLowerCase().includes("mx700"),
                        feedbackText: "Good, you're asking for NIBP calibration steps. The scenario specifies a 'Philips IntelliVue MX700'. Including the specific model in your prompt will help the AI give more tailored (though still simulated) instructions.",
                        promptQuality: "okay"
                    },
                    {
                        condition: (p) => p.toLowerCase().includes("calibrate") && p.toLowerCase().includes("nibp") && (p.toLowerCase().includes("steps") || p.toLowerCase().includes("procedure") || p.toLowerCase().includes("guide") || p.toLowerCase().includes("how to")) && (p.toLowerCase().includes("philips intellivue mx700") || p.toLowerCase().includes("mx700") || p.toLowerCase().includes("intellivue")),
                        feedbackText: "Excellent prompt! You've specified the task (NIBP calibration), the equipment model, and requested clear instructions. This is exactly how to get the best out of the AI assistant for procedural tasks.",
                        promptQuality: "good"
                    },
                    {
                        condition: (p) => true,
                        feedbackText: "For calibration tasks, always specify the equipment (NIBP module), the monitor model if known (e.g., 'Philips IntelliVue MX700' from the scenario), and ask for 'steps' or 'procedure'.",
                        promptQuality: "poor"
                    }
                ]
            },
            "alarm_volume_ge_b650": {
                text: "A new nurse is asking how to change the alarm volume on the GE CARESCAPE B650 monitor.",
                aiResponses: {
                    good: [
                        "To guide the nurse on changing the alarm volume on a GE CARESCAPE B650: 1. Direct them to touch the 'Alarms' quick key or the alarm status area on the main screen. 2. From there, they should look for an 'Alarm Setup', 'Audio Setup', or similar option. 3. Within that menu, they will find a setting for 'Alarm Volume' or 'Audio Level' which can be adjusted. Remind them that alarm volumes should be audible but not excessively loud, and to be aware of any facility policies regarding alarm levels. It's good practice to show them directly on the monitor if you are available.",
                        "For the GE CARESCAPE B650, adjusting alarm volume is usually done through the alarm settings menu. You can instruct the nurse as follows: 1. Access the main menu or tap an 'Alarms' button often found on the touchscreen interface. 2. Navigate to a submenu, typically labeled something like 'Alarm Configuration', 'Sound Settings', or 'Audio Adjust'. 3. Look for 'Master Alarm Volume' or a similar control to adjust the level. Ensure the nurse understands the importance of setting appropriate alarm levels according to unit guidelines and patient safety."
                    ],
                    okay: [
                        "Tell the nurse to look for a settings menu or an alarm icon on the GE B650 screen. The volume control should be in there, usually under 'Alarm Setup'.",
                        "GE CARESCAPE monitors usually have an intuitive alarm settings button. The nurse should press that and find the volume adjustment. It's pretty straightforward."
                    ],
                    poor: [
                        "The nurse should refer to the GE CARESCAPE B650 user manual for instructions on changing alarm volume.",
                        "Alarm volumes are critical and should ideally not be changed by nurses without specific unit training. Perhaps you should adjust it for them.",
                        "Just tell them to find the volume knob on the side or front of the monitor. Most monitors have one."
                    ]
                },
                feedbackRules: [
                    {
                        condition: (p) => p.length < 20 || (p.toLowerCase().includes("how to change volume") && !p.toLowerCase().includes("ge")),
                        feedbackText: "Your prompt is a bit general. Specify the equipment ('GE CARESCAPE B650'), the task ('change alarm volume'), and that it's for 'a new nurse' to get the most relevant simulated instructions.",
                        promptQuality: "poor"
                    },
                    {
                        condition: (p) => p.toLowerCase().includes("alarm volume") && (p.toLowerCase().includes("ge carescape b650") || p.toLowerCase().includes("b650")) && !(p.toLowerCase().includes("steps") || p.toLowerCase().includes("instruct") || p.toLowerCase().includes("guide") || p.toLowerCase().includes("teach")),
                        feedbackText: "Good! You've mentioned the monitor and the task. Now, try to phrase it as a request for instructions that you can relay, like 'How do I instruct a nurse to change alarm volume on GE B650?' or 'Provide steps to change alarm volume on GE B650 for a new user'.",
                        promptQuality: "okay"
                    },
                    {
                        condition: (p) => (p.toLowerCase().includes("steps") || p.toLowerCase().includes("instruct") || p.toLowerCase().includes("guide") || p.toLowerCase().includes("how to") || p.toLowerCase().includes("explain")) && p.toLowerCase().includes("alarm volume") && (p.toLowerCase().includes("ge carescape b650") || p.toLowerCase().includes("b650") || p.toLowerCase().includes("ge monitor")) && (p.toLowerCase().includes("nurse") || p.toLowerCase().includes("teach") || p.toLowerCase().includes("new user")),
                        feedbackText: "Perfect! You've included all key information: the specific monitor, the task, the context (for a new nurse), and a clear request for instructions. This helps the AI provide a comprehensive and useful response.",
                        promptQuality: "good"
                    },
                    {
                        condition: (p) => true,
                        feedbackText: "For requests like this, include the monitor model ('GE CARESCAPE B650'), the specific action ('change alarm volume'), and if relevant, the context (e.g., 'for a new nurse'). Ask for 'steps' or 'instructions'.",
                        promptQuality: "poor"
                    }
                ]
            }
        };

        const scenarioSelector = document.getElementById('scenario-selector');
        const scenarioTextElement = document.getElementById('scenario-text');
        const promptInputElement = document.getElementById('prompt-input');
        const generateButton = document.getElementById('generate-button');
        const aiResponseArea = document.getElementById('ai-response-area');
        const aiResponseTextElement = document.getElementById('ai-response-text');
        const feedbackArea = document.getElementById('feedback-area');
        const feedbackTextElement = document.getElementById('feedback-text');

        let currentScenarioId = null;

        function init() {
            // Populate scenario selector
            for (const id in scenariosData) {
                const option = document.createElement('option');
                option.value = id;
                // Make title more readable
                let title = id.replace(/_/g, ' ');
                title = title.charAt(0).toUpperCase() + title.slice(1);
                option.textContent = title;
                scenarioSelector.appendChild(option);
            }

            // Event listeners
            scenarioSelector.addEventListener('change', handleScenarioChange);
            generateButton.addEventListener('click', handleGenerateResponse);
            promptInputElement.addEventListener('input', checkPromptInput);


            // Load initial scenario
            if (scenarioSelector.options.length > 0) {
                currentScenarioId = scenarioSelector.value;
                displayScenario(currentScenarioId);
            }
            checkPromptInput(); // Initial check for button state
        }

        function handleScenarioChange() {
            currentScenarioId = scenarioSelector.value;
            displayScenario(currentScenarioId);
            // Clear previous results
            aiResponseArea.style.display = 'none';
            feedbackArea.style.display = 'none';
            promptInputElement.value = "";
            checkPromptInput();
        }

        function displayScenario(scenarioId) {
            const scenario = scenariosData[scenarioId];
            if (scenario) {
                scenarioTextElement.textContent = scenario.text;
            }
        }
        
        function checkPromptInput() {
            if (promptInputElement.value.trim() === "") {
                generateButton.disabled = true;
            } else {
                generateButton.disabled = false;
            }
        }


        function handleGenerateResponse() {
            if (!currentScenarioId || !scenariosData[currentScenarioId]) {
                alert("Please select a valid scenario first.");
                return;
            }

            const promptText = promptInputElement.value.trim();
            if (promptText === "") {
                alert("Please enter a prompt for the AI assistant.");
                return;
            }

            const scenario = scenariosData[currentScenarioId];
            let promptQuality = "poor";
            let feedbackMsg = "Could not evaluate prompt (default).";

            // Evaluate prompt
            for (const rule of scenario.feedbackRules) {
                if (rule.condition(promptText)) {
                    promptQuality = rule.promptQuality;
                    feedbackMsg = rule.feedbackText;
                    break;
                }
            }

            // Select AI response based on prompt quality
            let availableResponses = scenario.aiResponses[promptQuality];
            if (!availableResponses || availableResponses.length === 0) {
                // Fallback if a quality category is empty (shouldn't happen with good data)
                availableResponses = scenario.aiResponses.okay || scenario.aiResponses.poor || scenario.aiResponses.good;
                if (!availableResponses || availableResponses.length === 0) {
                     // Ultimate fallback
                    availableResponses = ["I'm having trouble formulating a response right now. Please try rephrasing your prompt."];
                }
            }
            const aiResponse = availableResponses[Math.floor(Math.random() * availableResponses.length)];

            // Display results
            aiResponseTextElement.textContent = aiResponse;
            aiResponseArea.style.display = 'block';

            feedbackTextElement.textContent = feedbackMsg;
            feedbackArea.className = `output-box prompt-feedback ${promptQuality}`; // Apply quality class for styling
            feedbackArea.style.display = 'block';
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', init);
    </script>

</body>
</html>
