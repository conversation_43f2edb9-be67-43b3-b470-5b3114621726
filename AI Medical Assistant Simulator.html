<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Medical Assistant Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        h1, h2, h3, h4 {
            color: #2c3e50;
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #1a5276;
        }

        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #fdfdfd;
        }

        .section h2, .section h3 {
            margin-top: 0;
            border-bottom: 2px solid #1abc9c;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        input[type="text"] {
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .config-options div {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .config-options input[type="checkbox"] {
            margin-right: 10px;
            width: 18px; /* Custom size */
            height: 18px; /* Custom size */
            accent-color: #1abc9c;
        }
         .config-options label {
            font-weight: normal;
            margin-bottom: 0; /* Reset margin for inline label */
        }


        button#get-response-btn {
            background-color: #1abc9c;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            display: block;
            width: 100%;
            margin-top: 15px;
        }

        button#get-response-btn:hover {
            background-color: #16a085;
        }

        #ai-response-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f6f3;
            border: 1px solid #d0e0dc;
            border-radius: 4px;
            min-height: 100px;
        }

        #ai-response-area h4 {
            color: #117a65;
            margin-top: 15px;
            margin-bottom: 8px;
        }
        #ai-response-area ul {
            padding-left: 20px;
        }
        #ai-response-area li {
            margin-bottom: 8px;
        }
        #ai-response-area code {
            background-color: #d6dbdf;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: "Courier New", Courier, monospace;
        }

        .prompt-examples {
            font-size: 0.9em;
            color: #555;
            margin-top: 5px;
        }

        ul.data-context-list {
            list-style-type: disc;
            padding-left: 20px;
        }
        ul.data-context-list li {
            margin-bottom: 5px;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            h2 {
                font-size: 1.4em;
            }

            input[type="text"], button#get-response-btn {
                font-size: 15px;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>AI Medical Assistant Simulator</h1>

        <div class="section scenario-details">
            <h2>Scenario</h2>
            <p>You are a medical engineer examining and maintaining vital signs monitoring devices (e.g., ECG, SpO2, NIBP monitors) in a hospital. You have access to an AI Assistant to help with your task.</p>
        </div>

        <div class="section data-context">
            <h2>Data Context</h2>
            <p>The AI Assistant has potential access to the following simulated data:</p>
            <ul class="data-context-list">
                <li>Operation Manuals for "VitalMonitor X Series" devices.</li>
                <li>Error Logs from the last 6 months for these devices (simulated as <code>error_logs_last_6_months.xlsx</code>).</li>
                <li>Parts Usage records detailing recent component replacements.</li>
            </ul>
        </div>

        <div class="section ai-interaction">
            <h2>Interact with the AI Assistant</h2>

            <div class="prompt-section">
                <label for="user-prompt">Your Question for the AI:</label>
                <input type="text" id="user-prompt" value="What are the most common errors related to these vital signs monitors?">
                <p class="prompt-examples"><em>Example prompts: "How do I fix a SpO2 reading that is too low?", "Suggest preventative maintenance for NIBP cuffs.", "Are there recurring issues with ECG modules?"</em></p>
            </div>

            <div class="config-options">
                <h3>AI Configuration (Data Access):</h3>
                <div>
                    <input type="checkbox" id="manuals-toggle" name="manuals">
                    <label for="manuals-toggle">Access Operation Manuals (VitalMonitor X Series)</label>
                </div>
                <div>
                    <input type="checkbox" id="error-logs-toggle" name="error-logs">
                    <label for="error-logs-toggle">Access Historical Error Logs (Last 6 Months)</label>
                </div>
                <div>
                    <input type="checkbox" id="parts-usage-toggle" name="parts-usage">
                    <label for="parts-usage-toggle">Access Parts Usage Records</label>
                </div>
            </div>
            <button id="get-response-btn">Ask AI Assistant</button>

            <div class="response-section">
                <h3>AI Assistant Response:</h3>
                <div id="ai-response-area">
                    <p>Configure the AI's data access and ask a question to see the response.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('get-response-btn').addEventListener('click', function() {
            const userQuery = document.getElementById('user-prompt').value || "No specific query provided.";
            const manualsEnabled = document.getElementById('manuals-toggle').checked;
            const errorLogsEnabled = document.getElementById('error-logs-toggle').checked;
            const partsUsageEnabled = document.getElementById('parts-usage-toggle').checked;

            const responseArea = document.getElementById('ai-response-area');
            responseArea.innerHTML = generateAIResponse(manualsEnabled, errorLogsEnabled, partsUsageEnabled, userQuery);
        });

        function generateAIResponse(manuals, errorLogs, partsUsage, query) {
            let htmlResponse = `<h4>Analysis for: "${query}"</h4>`;
            
            let sources = [];
            if (manuals) sources.push("Operation Manuals");
            if (errorLogs) sources.push("Error Logs");
            if (partsUsage) sources.push("Parts Usage Records");

            if (sources.length > 0) {
                htmlResponse += `<p><em>Accessing: ${sources.join(', ')}...</em></p><hr>`;
            } else {
                htmlResponse += `<p><em>No specific data sources selected. Providing general guidance.</em></p><hr>`;
            }

            let commonErrorsContent = "";
            let preventativeMaintenanceContent = "";
            let troubleshootingContent = "";
            let componentInsightsContent = "";

            // --- Content Generation Logic ---

            // General advice if no sources
            if (!manuals && !errorLogs && !partsUsage) {
                preventativeMaintenanceContent += "<ul>";
                preventativeMaintenanceContent += "<li>Ensure devices are regularly cleaned and visually inspected.</li>";
                preventativeMaintenanceContent += "<li>Follow standard operating procedures for device checks and calibrations if known.</li>";
                preventativeMaintenanceContent += "<li>For specific issues, always try to consult device documentation or experienced colleagues if available.</li>";
                preventativeMaintenanceContent += "</ul>";
            }

            // Manuals-specific content
            if (manuals) {
                commonErrorsContent += "<p>Based on 'VitalMonitor X Series Manuals':</p><ul>";
                commonErrorsContent += "<li>Commonly reported issues often relate to sensor connectivity or calibration drift. Refer to <code>Manual_VitalMonitorX_Section3.1_Troubleshooting.pdf#page=15</code> for sensor checks.</li>";
                commonErrorsContent += "<li>Incorrect alarm settings can be mistaken for device errors. See <code>Manual_VitalMonitorX_Section2.5_Alarms.pdf#page=10</code>.</li></ul>";
                
                preventativeMaintenanceContent += "<p>From 'VitalMonitor X Series Manuals':</p><ul>";
                preventativeMaintenanceContent += "<li>Perform daily self-tests as described in <code>Manual_VitalMonitorX_DailyChecks.pdf#page=3</code>.</li>";
                preventativeMaintenanceContent += "<li>NIBP module calibration is recommended every 6 months. Details in <code>Manual_VitalMonitorX_ServiceGuide.pdf#section_calibration</code>.</li>";
                preventativeMaintenanceContent += "<li>Regularly inspect and clean SpO2 sensors as per <code>Manual_VitalMonitorX_SpO2_Care.pdf#page=7</code> to ensure accurate readings.</li></ul>";

                troubleshootingContent += "<p>Guidance from 'VitalMonitor X Series Manuals':</p><ul>";
                troubleshootingContent += "<li>For 'Low SpO2 Reading', check sensor placement and patient perfusion. Consult <code>Manual_VitalMonitorX_SpO2_Troubleshooting.pdf#page=23</code>.</li>";
                troubleshootingContent += "<li>If an 'ECG Lead Off' warning appears, verify electrode contact and cable integrity as per <code>Manual_VitalMonitorX_ECG_Guide.pdf#section_leads</code>.</li></ul>";
            }

            // Error Logs-specific content
            if (errorLogs) {
                commonErrorsContent += "<p>Analysis of <code>error_logs_last_6_months.xlsx</code> reveals:</p><ul>";
                commonErrorsContent += "<li>Error Code <strong>E05 (Sensor Disconnect)</strong> is the most frequent, logged 42 times across various units.</li>";
                commonErrorsContent += "<li>There's a notable increase in <strong>E12 (NIBP Cuff Pressure Fail)</strong> in Ward C devices over the past month (15 occurrences).</li>";
                commonErrorsContent += "<li><strong>B01 (Battery Low Warning)</strong> appears consistently on devices older than 3 years.</li></ul>";

                if (manuals) { // Cross-referencing example
                    troubleshootingContent += "<p>Cross-referencing Error Logs with Manuals:</p><ul>";
                    troubleshootingContent += "<li>The frequent E05 (Sensor Disconnect) errors align with manual advice (<code>Manual_VitalMonitorX_Section3.1_Troubleshooting.pdf#page=15</code>) to regularly check sensor cable integrity.</li></ul>";
                }
            }

            // Parts Usage-specific content
            if (partsUsage) {
                componentInsightsContent += "<p>Insights from Parts Usage Records:</p><ul>";
                componentInsightsContent += "<li>SpO2 sensors (Part #SPO2-SEN-VMX01) have been replaced 28 times in the last 6 months, indicating high turnover or potential fragility.</li>";
                componentInsightsContent += "<li>Replacement of NIBP cuffs (Part #NIBP-CUFF-ADULT) is low; consider checking cuff conditions during routine maintenance.</li>";
                componentInsightsContent += "<li>Batteries (Part #BATT-VMX-STD) for units SN: VMX1001-VMX1020 were replaced 3 months ago.</li></ul>";

                if (errorLogs) { // Cross-referencing example
                     componentInsightsContent += "<p>Correlating Parts Usage with Error Logs:</p><ul>";
                     componentInsightsContent += "<li>The high replacement rate of SpO2 sensors (Part #SPO2-SEN-VMX01) may be linked to the frequent E05 (Sensor Disconnect) errors found in <code>error_logs_last_6_months.xlsx</code>. This suggests investigating sensor handling or connection points.</li></ul>";
                }
                 if (manuals) { // Cross-referencing example
                     preventativeMaintenanceContent += "<p>Considering Parts Usage and Manuals:</p><ul>";
                     preventativeMaintenanceContent += "<li>Given the SpO2 sensor replacement frequency, ensure staff are following cleaning and handling procedures outlined in <code>Manual_VitalMonitorX_SpO2_Care.pdf#page=7</code> to prolong sensor life.</li></ul>";
                }
            }
            
            // Assembling the response sections
            if (commonErrorsContent) {
                htmlResponse += `<h4>Common Errors & Diagnostics</h4>${commonErrorsContent}`;
            }
            if (preventativeMaintenanceContent) {
                htmlResponse += `<h4>Preventative Maintenance Steps</h4>${preventativeMaintenanceContent}`;
            }
            if (troubleshootingContent) {
                htmlResponse += `<h4>Troubleshooting Guide</h4>${troubleshootingContent}`;
            }
            if (componentInsightsContent) {
                htmlResponse += `<h4>Component & Parts Insights</h4>${componentInsightsContent}`;
            }

            // Final check if any content was added beyond the intro
            const placeholderForEmpty = `<p><em>Accessing: ${sources.join(', ')}...</em></p><hr>`;
            const placeholderForNoSource = `<p><em>No specific data sources selected. Providing general guidance.</em></p><hr>`;
            if (htmlResponse.endsWith(placeholderForEmpty) || htmlResponse.endsWith(placeholderForNoSource)) {
                 htmlResponse += "<p>Select data sources and ask a question to get a specific analysis. For now, please ensure all devices are powered on and visually inspected.</p>";
            }


            return htmlResponse;
        }
    </script>
</body>
</html>
