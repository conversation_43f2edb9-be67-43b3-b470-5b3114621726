<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI & The Future of Medicine</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 15px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #005a9c; /* Professional Blue */
            margin-bottom: 25px;
        }

        .controls {
            margin-bottom: 25px;
            padding: 15px;
            background-color: #e9f2f9; /* Light blue background for controls */
            border-radius: 6px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: flex-end;
        }

        .control-group {
            flex: 1;
            min-width: 200px; /* Ensures controls don't get too squeezed */
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #004085; /* Darker blue for labels */
        }

        select, input[type="range"] {
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            box-sizing: border-box;
            background-color: #fff;
        }
        
        input[type="range"] {
            padding: 0; /* Remove default padding for range inputs */
             accent-color: #005a9c;
        }

        .timeline-slider-group {
            flex: 2; /* Allow slider to take more space */
            display: flex;
            flex-direction: column;
        }
        .timeline-slider-group .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .timeline-slider-group input[type="range"] {
            flex-grow: 1;
        }

        #yearDisplay {
            font-weight: bold;
            color: #005a9c;
            white-space: nowrap;
            font-size: 0.9em;
        }

        .section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fdfdfd;
        }

        .section h3 {
            margin-top: 0;
            color: #005a9c;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }

        ul {
            list-style: none;
            padding-left: 0;
        }

        #taskList li, #skillsList li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
            font-size: 0.95em;
        }

        #taskList li::before, #skillsList li::before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #005a9c;
            font-size: 0.8em;
            top: 3px;
        }
        
        .faded {
            opacity: 0.6;
            text-decoration: line-through;
            color: #6c757d; /* Grey */
        }
        .faded::before { color: #6c757d; content: "✘"; top: 2px;}

        .modified {
            font-style: italic;
            color: #E67E22; /* Orange for modified */
        }
        .modified::before { color: #E67E22; content: "✎"; top: 2px; }
        
        .new-emphasis {
            font-weight: 600; /* Semibold */
            color: #27AE60; /* Green for new/positive */
        }
        .new-emphasis::before { color: #27AE60; content: "★"; top: 2px; }

        .new-skill {
            font-weight: 600; /* Semibold */
            color: #2980B9; /* Different Blue for new skills */
        }
        .new-skill::before { color: #2980B9; content: "💡"; top: 2px; }


        .outlook-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 5px;
        }
        .outlook-bar-bg {
            flex-grow: 1;
            background-color: #e9ecef;
            border-radius: 10px; /* More rounded */
            height: 22px;
            overflow: hidden;
            position: relative;
        }
        #jobOutlookBar {
            height: 100%;
            width: 0%; /* Initial width */
            transition: width 0.4s ease-out, background-color 0.4s ease-out;
            border-radius: 10px; /* Match parent */
        }
        #jobOutlookValue {
            font-weight: bold;
            font-size: 1em;
            color: #333;
            min-width: 40px; /* Space for "100%" */
            text-align: right;
        }
        #jobOutlookText {
            font-style: italic;
            color: #555;
            font-size: 0.9em;
        }

        .encouragement {
            margin-top: 25px;
            padding: 15px;
            background-color: #e6f7ff; /* Lighter blue for encouragement */
            border-left: 5px solid #005a9c;
            color: #004085;
            font-size: 0.95em;
        }
        .encouragement p { margin: 0;}

        @media (max-width: 650px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            .control-group {
                min-width: 100%;
            }
            .timeline-slider-group .slider-container {
                flex-direction: column;
                align-items: stretch;
            }
            #yearDisplay {
                text-align: center;
                margin-top: 8px;
            }
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.5em; }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>The Doctor & AI: Navigating the Future</h1>

        <div class="controls">
            <div class="control-group">
                <label for="specialtySelector">Select Medical Specialty:</label>
                <select id="specialtySelector"></select>
            </div>
            <div class="control-group timeline-slider-group">
                <label for="timelineSlider">Years in the Future:</label>
                <div class="slider-container">
                    <input type="range" id="timelineSlider" min="0" max="15" value="0" step="1">
                    <span id="yearDisplay">0 Years</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>Scenario: Dr. <span id="doctorNamePlaceholder"></span>, <span id="specialtyNameDisplay">Radiologist</span></h3>
            <p id="scenarioDescription"></p>
        </div>

        <div class="section">
            <h3>Doctor's Evolving Tasks</h3>
            <ul id="taskList"></ul>
        </div>

        <div class="section">
            <h3>Key Skills for the Future</h3>
            <ul id="skillsList"></ul>
        </div>

        <div class="section">
            <h3>Job Outlook for <span id="jobOutlookSpecialtyName">Radiology</span></h3>
            <div class="outlook-container">
                <div class="outlook-bar-bg">
                    <div id="jobOutlookBar"></div>
                </div>
                <span id="jobOutlookValue"></span>
            </div>
            <p id="jobOutlookText"></p>
        </div>

        <div class="encouragement">
            <p><strong>Embracing Change:</strong> AI in medicine is poised to augment, not replace, skilled physicians. By adapting and integrating these new tools, doctors can enhance efficiency, reduce mundane workloads, improve diagnostic accuracy, and ultimately deliver superior patient outcomes. The future favors those who learn to collaborate with AI, focusing on complex problem-solving, patient-centric care, and pioneering medical innovations.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const specialtiesData = {
                radiology: {
                    name: "Radiology",
                    doctorName: "Carter",
                    initialScenario: "Dr. Carter, a radiologist, spends her day interpreting medical images like X-rays, CT scans, and MRIs to diagnose diseases and injuries. She consults with other physicians and dictates reports.",
                    initialTasks: [
                        { id: "rad_task_1", text: "Interpret X-rays for fractures.", currentText: "Interpret X-rays for fractures.", status: "active" },
                        { id: "rad_task_2", text: "Analyze CT scans for tumors.", currentText: "Analyze CT scans for tumors.", status: "active" },
                        { id: "rad_task_3", text: "Review MRI scans for soft tissue injuries.", currentText: "Review MRI scans for soft tissue injuries.", status: "active" },
                        { id: "rad_task_4", text: "Dictate diagnostic reports.", currentText: "Dictate diagnostic reports.", status: "active" },
                        { id: "rad_task_5", text: "Consult with referring physicians.", currentText: "Consult with referring physicians.", status: "active" }
                    ],
                    initialSkills: [
                        "Medical image interpretation", "Pattern recognition", "Anatomy & Pathology knowledge", "Communication with clinicians"
                    ],
                    initialJobOutlook: { value: 80, description: "High Demand" },
                    timelineEvents: [
                        {
                            yearThreshold: 5,
                            scenarioUpdate: "AI tools now assist Dr. Carter by pre-screening images, flagging potential abnormalities, and drafting initial reports. She spends more time verifying AI findings and handling complex cases.",
                            taskChanges: [
                                { id: "rad_task_1", newText: "Verify AI-flagged X-ray interpretations.", status: "modified" },
                                { id: "rad_task_2", newText: "Review AI-prioritized CT scans for complex tumors.", status: "modified" },
                                { id: "rad_task_4", newText: "Review and edit AI-generated diagnostic reports.", status: "modified" },
                                { id: "rad_task_new_1", text: "Manage AI image analysis workflow.", status: "new"},
                            ],
                            newSkills: ["AI output validation", "Understanding AI limitations", "Basic data literacy"],
                            jobOutlook: { value: 75, description: "Stable, role evolving with AI" }
                        },
                        {
                            yearThreshold: 10,
                            scenarioUpdate: "AI handles most routine image analysis. Dr. Carter focuses on highly complex, atypical cases, quality control of AI systems, research, and developing new imaging protocols with AI integration.",
                            taskChanges: [
                                { id: "rad_task_1", status: "faded" },
                                { id: "rad_task_2", newText: "Oversee AI diagnostic systems for oncology imaging.", status: "modified" },
                                { id: "rad_task_3", newText: "Lead research on AI-driven multimodal imaging techniques.", status: "modified" },
                                { id: "rad_task_4", status: "faded" },
                                { id: "rad_task_5", newText: "Collaborate with AI developers to refine algorithms.", status: "modified" },
                                { id: "rad_task_new_1", newText: "Implement and monitor AI quality assurance protocols.", status: "modified"},
                                { id: "rad_task_new_2", text: "Train junior radiologists on AI tool usage.", status: "new"},
                            ],
                            newSkills: ["AI system management", "Medical data science principles", "Ethical considerations of AI in imaging", "Research methodology with AI"],
                            jobOutlook: { value: 65, description: "Demand for specialized, AI-savvy radiologists" }
                        },
                        {
                            yearThreshold: 15,
                            scenarioUpdate: "Dr. Carter is an 'AI-augmented radiologist'. She manages fleets of AI diagnostic tools, ensures their ethical application, and pioneers new diagnostic pathways. Her role is less about pixel-level interpretation and more about oversight, innovation, and complex systemic problem-solving.",
                            taskChanges: [
                                { id: "rad_task_2", newText: "Direct AI diagnostic strategy for the imaging department.", status: "modified" },
                                { id: "rad_task_3", newText: "Ensure ethical AI deployment and mitigate algorithmic bias.", status: "modified" },
                                { id: "rad_task_5", newText: "Educate multidisciplinary teams on advanced AI capabilities.", status: "modified" },
                                { id: "rad_task_new_1", newText: "Develop policies for AI in diagnostic imaging.", status: "modified"},
                                { id: "rad_task_new_2", newText: "Lead clinical trials for novel AI diagnostic tools.", status: "modified"},
                                { id: "rad_task_new_3", text: "Advise on procurement and integration of new AI technologies.", status: "new"},
                            ],
                            newSkills: ["AI ethics and governance in healthcare", "Medical AI strategy and leadership", "Interdisciplinary team leadership (AI/Clinical)", "Change management"],
                            jobOutlook: { value: 70, description: "New leadership & oversight roles emerge, overall demand shifts" }
                        }
                    ]
                },
                pathology: {
                    name: "Pathology",
                    doctorName: "Miller",
                    initialScenario: "Dr. Miller, a pathologist, examines tissue samples and body fluids under a microscope to diagnose diseases like cancer. He prepares detailed reports for clinicians.",
                    initialTasks: [
                        { id: "path_task_1", text: "Microscopic examination of tissue biopsies.", currentText: "Microscopic examination of tissue biopsies.", status: "active" },
                        { id: "path_task_2", text: "Analyze blood smears and cytology samples.", currentText: "Analyze blood smears and cytology samples.", status: "active" },
                        { id: "path_task_3", text: "Perform immunohistochemistry (IHC) stain interpretation.", currentText: "Perform immunohistochemistry (IHC) stain interpretation.", status: "active" },
                        { id: "path_task_4", text: "Prepare and sign out pathology reports.", currentText: "Prepare and sign out pathology reports.", status: "active" },
                        { id: "path_task_5", text: "Participate in tumor board meetings.", currentText: "Participate in tumor board meetings.", status: "active" }
                    ],
                    initialSkills: [ "Microscopy skills", "Histological pattern recognition", "Molecular pathology knowledge", "Diagnostic acumen" ],
                    initialJobOutlook: { value: 75, description: "Steady Demand" },
                    timelineEvents: [
                        {
                            yearThreshold: 5,
                            scenarioUpdate: "AI-powered image analysis tools assist Dr. Miller by pre-screening slides, quantifying biomarkers, and identifying regions of interest. He verifies AI findings and focuses on complex interpretations.",
                            taskChanges: [
                                { id: "path_task_1", newText: "Review AI-assisted slide analysis for biopsies.", status: "modified" },
                                { id: "path_task_2", newText: "Verify AI-detected abnormalities in cytology.", status: "modified" },
                                { id: "path_task_3", newText: "Interpret AI-quantified IHC markers.", status: "modified" },
                                { id: "path_task_new_1", text: "Manage digital pathology workflows and AI tool integration.", status: "new"}
                            ],
                            newSkills: ["Digital pathology systems", "AI in image analysis (pathology)", "Computational pathology basics"],
                            jobOutlook: { value: 70, description: "Evolving role, efficiency gains" }
                        },
                        {
                            yearThreshold: 10,
                            scenarioUpdate: "AI handles a significant portion of routine slide screening and quantitative analysis. Dr. Miller's role shifts to overseeing AI quality, interpreting complex or discordant cases, and integrating genomic data with AI-derived morphological features.",
                            taskChanges: [
                                { id: "path_task_1", newText: "Oversee AI-driven primary screening of biopsies, focus on exceptions.", status: "modified" },
                                { id: "path_task_2", status: "faded" },
                                { id: "path_task_3", newText: "Integrate AI-derived image features with genomic data for diagnosis.", status: "modified" },
                                { id: "path_task_4", newText: "Co-author reports with AI-generated sections, provide expert oversight.", status: "modified" },
                                { id: "path_task_new_1", newText: "Validate and implement new AI pathology algorithms.", status: "modified"},
                                { id: "path_task_new_2", text: "Contribute to AI model training with expert annotations.", status: "new"}
                            ],
                            newSkills: ["Integrated diagnostics (omics + imaging AI)", "AI algorithm validation", "Data management for AI pathology"],
                            jobOutlook: { value: 60, description: "Specialized roles in computational and integrated pathology" }
                        },
                        {
                            yearThreshold: 15,
                            scenarioUpdate: "Dr. Miller is a 'computational pathologist' leading a team that develops and deploys AI tools for diagnostics and prognostics. He focuses on research, ethical AI use, and translating AI discoveries into clinical practice.",
                            taskChanges: [
                                { id: "path_task_1", status: "faded" },
                                { id: "path_task_3", newText: "Lead development of AI-powered predictive biomarkers.", status: "modified" },
                                { id: "path_task_4", status: "faded" },
                                { id: "path_task_5", newText: "Present AI-driven insights at multidisciplinary tumor boards.", status: "modified" },
                                { id: "path_task_new_1", newText: "Establish governance for AI in pathology.", status: "modified"},
                                { id: "path_task_new_2", newText: "Design studies to assess clinical impact of AI pathology tools.", status: "modified"},
                                { id: "path_task_new_3", text: "Collaborate with industry to co-develop next-gen AI solutions.", status: "new"}
                            ],
                            newSkills: ["AI research in pathology", "Clinical trial design for AI", "Regulatory aspects of AI medical devices", "Pathomics data analysis"],
                            jobOutlook: { value: 65, description: "High demand for leaders in AI-driven pathology innovation" }
                        }
                    ]
                },
                dermatology: {
                    name: "Dermatology",
                    doctorName: "Chen",
                    initialScenario: "Dr. Chen, a dermatologist, diagnoses and treats skin conditions. Her day involves patient consultations, visual skin examinations, performing minor procedures like biopsies, and prescribing treatments.",
                    initialTasks: [
                        { id: "derm_task_1", text: "Visual examination of skin lesions and rashes.", currentText: "Visual examination of skin lesions and rashes.", status: "active" },
                        { id: "derm_task_2", text: "Perform skin biopsies and other minor procedures.", currentText: "Perform skin biopsies and other minor procedures.", status: "active" },
                        { id: "derm_task_3", text: "Interpret dermoscopy images.", currentText: "Interpret dermoscopy images.", status: "active" },
                        { id: "derm_task_4", text: "Prescribe medications and topical treatments.", currentText: "Prescribe medications and topical treatments.", status: "active" },
                        { id: "derm_task_5", text: "Patient counseling and education on skin health.", currentText: "Patient counseling and education on skin health.", status: "active" }
                    ],
                    initialSkills: [ "Clinical dermatology expertise", "Visual diagnostic skills", "Dermoscopy proficiency", "Procedural skills", "Patient communication" ],
                    initialJobOutlook: { value: 85, description: "Very High Demand" },
                    timelineEvents: [
                        {
                            yearThreshold: 5,
                            scenarioUpdate: "AI-powered smartphone apps and clinic tools assist Dr. Chen by providing initial risk assessments for skin lesions from patient-submitted or dermoscopy images. This helps triage patients and augment her diagnostic process.",
                            taskChanges: [
                                { id: "derm_task_1", newText: "Review AI-assisted risk scores for skin lesions prior to examination.", status: "modified" },
                                { id: "derm_task_3", newText: "Use AI-enhanced dermoscopy for improved feature detection.", status: "modified" },
                                { id: "derm_task_new_1", text: "Interpret teledermatology submissions with AI pre-analysis.", status: "new"}
                            ],
                            newSkills: ["Understanding AI in dermatology imaging", "Teledermatology platform usage", "Interpreting AI confidence scores"],
                            jobOutlook: { value: 80, description: "High demand, AI enhances efficiency" }
                        },
                        {
                            yearThreshold: 10,
                            scenarioUpdate: "AI tools are integrated into EMRs, providing real-time diagnostic suggestions and treatment pathways based on image analysis and patient data. Dr. Chen validates AI suggestions, handles complex cases, and focuses more on personalized treatment plans and advanced procedures.",
                            taskChanges: [
                                { id: "derm_task_1", newText: "Confirm or override AI diagnostic suggestions for common conditions.", status: "modified" },
                                { id: "derm_task_3", status: "faded" },
                                { id: "derm_task_4", newText: "Develop personalized treatment plans leveraging AI predictive analytics.", status: "modified" },
                                { id: "derm_task_new_1", newText: "Manage patients remotely using AI-monitored data.", status: "modified"},
                                { id: "derm_task_new_2", text: "Perform more complex cosmetic and surgical procedures.", status: "new"}
                            ],
                            newSkills: ["AI-assisted decision support", "Personalized medicine principles", "Remote patient monitoring technologies", "Advanced dermatologic surgery"],
                            jobOutlook: { value: 75, description: "Strong demand, focus shifts to complex care and procedures" }
                        },
                        {
                            yearThreshold: 15,
                            scenarioUpdate: "Dr. Chen utilizes sophisticated AI for early detection of rare skin diseases, predicting treatment responses, and monitoring chronic conditions. Her role involves managing AI systems, contributing to their improvement, and leading innovations in AI-driven dermatological care.",
                            taskChanges: [
                                { id: "derm_task_1", newText: "Oversee AI-driven population screening programs for skin cancer.", status: "modified" },
                                { id: "derm_task_2", newText: "Focus on complex, AI-resistant diagnostic challenges and rare diseases.", status: "modified" },
                                { id: "derm_task_4", newText: "Utilize AI for predicting drug efficacy and adverse events.", status: "modified" },
                                { id: "derm_task_5", newText: "Counsel patients on AI-derived risk profiles and preventative strategies.", status: "modified" },
                                { id: "derm_task_new_1", status: "faded"},
                                { id: "derm_task_new_2", newText: "Lead research in AI applications for dermatological therapeutics.", status: "modified"},
                                { id: "derm_task_new_3", text: "Develop ethical guidelines for AI use in dermatology.", status: "new"}
                            ],
                            newSkills: ["AI in genomics and personalized dermatology", "Management of AI healthcare platforms", "Ethical AI in patient-facing roles", "Translational research with AI"],
                            jobOutlook: { value: 70, description: "Demand for AI-expert dermatologists and innovators" }
                        }
                    ]
                }
            };

            let currentSpecialtyKey = 'radiology';
            let currentYear = 0;

            const specialtySelector = document.getElementById('specialtySelector');
            const timelineSlider = document.getElementById('timelineSlider');
            const yearDisplay = document.getElementById('yearDisplay');
            const doctorNamePlaceholder = document.getElementById('doctorNamePlaceholder');
            const specialtyNameDisplay = document.getElementById('specialtyNameDisplay');
            const scenarioDescription = document.getElementById('scenarioDescription');
            const taskListUl = document.getElementById('taskList');
            const skillsListUl = document.getElementById('skillsList');
            const jobOutlookSpecialtyName = document.getElementById('jobOutlookSpecialtyName');
            const jobOutlookBar = document.getElementById('jobOutlookBar');
            const jobOutlookValue = document.getElementById('jobOutlookValue');
            const jobOutlookText = document.getElementById('jobOutlookText');

            function initializeApp() {
                for (const key in specialtiesData) {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = specialtiesData[key].name;
                    specialtySelector.appendChild(option);
                }
                specialtySelector.value = currentSpecialtyKey;
                timelineSlider.value = currentYear;
                updateView();
            }

            function updateView() {
                const specialtyData = specialtiesData[currentSpecialtyKey];
                currentYear = parseInt(timelineSlider.value);
                yearDisplay.textContent = `${currentYear} Years`;

                doctorNamePlaceholder.textContent = specialtyData.doctorName;
                specialtyNameDisplay.textContent = specialtyData.name;
                jobOutlookSpecialtyName.textContent = specialtyData.name;

                updateScenario(specialtyData, currentYear);
                updateTasks(specialtyData, currentYear);
                updateSkills(specialtyData, currentYear);
                updateJobOutlook(specialtyData, currentYear);
            }

            function updateScenario(data, year) {
                let scenarioText = data.initialScenario;
                data.timelineEvents.forEach(event => {
                    if (year >= event.yearThreshold && event.scenarioUpdate) {
                        scenarioText = event.scenarioUpdate;
                    }
                });
                scenarioDescription.textContent = scenarioText.replace("Dr. Carter", "Dr. " + data.doctorName)
                                                            .replace("Dr. Miller", "Dr. " + data.doctorName)
                                                            .replace("Dr. Chen", "Dr. " + data.doctorName);
            }

            function updateTasks(data, year) {
                taskListUl.innerHTML = '';
                let currentTasks = JSON.parse(JSON.stringify(data.initialTasks));
                let addedTaskIds = new Set(currentTasks.map(t => t.id));

                data.timelineEvents.forEach(event => {
                    if (year >= event.yearThreshold && event.taskChanges) {
                        event.taskChanges.forEach(change => {
                            let task = currentTasks.find(t => t.id === change.id);
                            if (task) {
                                if (change.newText) task.currentText = change.newText;
                                if (change.status) task.status = change.status;
                            } else if (change.status === 'new' && change.text && !addedTaskIds.has(change.id)) {
                                currentTasks.push({
                                    id: change.id,
                                    text: change.text,
                                    currentText: change.newText || change.text,
                                    status: 'active' // New tasks default to active, can be changed by same event if status is also set
                                });
                                if(change.status) currentTasks[currentTasks.length-1].status = change.status; // Apply status if specified for new
                                addedTaskIds.add(change.id);
                            }
                        });
                    }
                });
                
                const initialTaskIds = new Set(data.initialTasks.map(t => t.id));
                currentTasks.forEach(task => {
                    const li = document.createElement('li');
                    li.textContent = task.currentText;
                    li.className = ''; // Reset classes

                    if (task.status === 'faded') {
                        li.classList.add('faded');
                    } else if (task.status === 'modified') {
                        li.classList.add('modified');
                    }
                    
                    // Add new-emphasis if it's not an initial task and not faded
                    if (!initialTaskIds.has(task.id) && task.status !== 'faded') {
                         li.classList.add('new-emphasis');
                    }
                    taskListUl.appendChild(li);
                });
            }

            function updateSkills(data, year) {
                skillsListUl.innerHTML = '';
                let currentSkills = new Set(data.initialSkills);
                data.timelineEvents.forEach(event => {
                    if (year >= event.yearThreshold && event.newSkills) {
                        event.newSkills.forEach(skill => currentSkills.add(skill));
                    }
                });

                currentSkills.forEach(skill => {
                    const li = document.createElement('li');
                    li.textContent = skill;
                    if (!data.initialSkills.includes(skill)) {
                        li.classList.add('new-skill');
                    }
                    skillsListUl.appendChild(li);
                });
            }

            function updateJobOutlook(data, year) {
                let outlook = data.initialJobOutlook;
                data.timelineEvents.forEach(event => {
                    if (year >= event.yearThreshold && event.jobOutlook) {
                        outlook = event.jobOutlook;
                    }
                });

                jobOutlookBar.style.width = `${outlook.value}%`;
                jobOutlookValue.textContent = `${outlook.value}%`;
                jobOutlookText.textContent = outlook.description;

                if (outlook.value >= 70) {
                    jobOutlookBar.style.backgroundColor = '#27AE60'; // Green
                } else if (outlook.value >= 40) {
                    jobOutlookBar.style.backgroundColor = '#F39C12'; // Amber/Orange
                } else {
                    jobOutlookBar.style.backgroundColor = '#E74C3C'; // Red
                }
            }

            specialtySelector.addEventListener('change', (e) => {
                currentSpecialtyKey = e.target.value;
                timelineSlider.value = 0; // Reset timeline
                updateView();
            });

            timelineSlider.addEventListener('input', () => { // 'input' for live updates
                updateView();
            });

            initializeApp();
        });
    </script>
</body>
</html>
