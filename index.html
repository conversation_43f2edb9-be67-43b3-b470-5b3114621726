<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#2563eb" />
    <meta
      name="description"
      content="Comprehensive AI in Medicine and Engineering Learning Platform featuring Medical Imaging Modalities, Clinical AI Applications, and Biomedical Engineering Solutions"
    />
    <meta name="keywords" content="AI, Medicine, Medical Imaging, Radiology, CT, MRI, X-ray, Ultrasound, PET, Biomedical Engineering, Healthcare Technology, Machine Learning, Deep Learning, Computer Vision" />
    <meta name="author" content="AI in Medicine Future" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="AI in Medicine & Engineering LMS" />
    <meta property="og:description" content="Advanced learning platform for AI applications in medical imaging, clinical practice, and biomedical engineering" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="AI in Medicine & Engineering LMS" />
    <meta property="twitter:description" content="Learn AI applications in medical imaging, radiology, and healthcare technology" />

    <title>AI in Medicine & Engineering LMS - Medical Imaging & Healthcare AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"},
              secondary: {"50":"#f0f9ff","100":"#e0f2fe","200":"#bae6fd","300":"#7dd3fc","400":"#38bdf8","500":"#0ea5e9","600":"#0284c7","700":"#0369a1","800":"#075985","900":"#0c4a6e","950":"#082f49"},
              neutral: {"50":"#f8fafc","100":"#f1f5f9","200":"#e2e8f0","300":"#cbd5e1","400":"#94a3b8","500":"#64748b","600":"#475569","700":"#334155","800":"#1e293b","900":"#0f172a","950":"#020617"}
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
    "react-router-dom": "https://esm.sh/react-router-dom@6.25.1",
    "@heroicons/react/24/solid": "https://esm.sh/@heroicons/react@2.0.18/24/solid",
    "@heroicons/react/24/outline": "https://esm.sh/@heroicons/react@2.0.18/24/outline",
    "react-dom/": "https://esm.sh/react-dom@18.2.0/",
    "react/": "https://esm.sh/react@18.2.0/",
    "@heroicons/react/": "https://esm.sh/@heroicons/react@2.0.18/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-neutral-100 antialiased">
    <noscript>
      <div class="flex items-center justify-center min-h-screen bg-neutral-100">
        <div class="text-center p-8 bg-white rounded-lg shadow-lg max-w-md mx-4">
          <h1 class="text-2xl font-bold text-neutral-800 mb-4">JavaScript Required</h1>
          <p class="text-neutral-600 mb-4">
            This AI in Medicine & Engineering Learning Platform requires JavaScript to function properly.
          </p>
          <p class="text-sm text-neutral-500">
            Please enable JavaScript in your browser settings and refresh the page to access the medical imaging modules and interactive content.
          </p>
        </div>
      </div>
    </noscript>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"></div>
        <h2 class="text-xl font-semibold text-neutral-700 mb-2">Loading AI in Medicine LMS</h2>
        <p class="text-neutral-500">Preparing your medical imaging learning experience...</p>
      </div>
    </div>

    <div id="root"></div>

    <script>
      // Hide loading indicator once React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingIndicator = document.getElementById('loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.style.opacity = '0';
            loadingIndicator.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingIndicator.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>

    <script type="module" src="/index.tsx"></script>
  </body>
</html>