
import React, { useState, useCallback, useEffect } from 'react';
import { sendMessageInChat, isGeminiAvailable, resetChat } from '../../services/geminiService';
import { ChatMessage } from '../../types';
import { PaperAirplaneIcon, ArrowPathIcon } from '@heroicons/react/24/solid';


interface AIPromptEngineeringPracticeProps {
  moduleTitle: string;
}

const AIPromptEngineeringPractice: React.FC<AIPromptEngineeringPracticeProps> = ({ moduleTitle }) => {
  const [apiKeyAvailable, setApiKeyAvailable] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [systemInstruction, setSystemInstruction] = useState('You are a helpful Biomedical Engineering Assistant. Provide concise and accurate information related to biomedical engineering, AI in medicine, and clinical applications. If asked about topics outside this scope, politely state your specialization.');
  
  useEffect(() => {
    setApiKeyAvailable(isGeminiAvailable());
    // Reset chat when component mounts or system instruction changes to ensure new context
    resetChat(); 
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // System instruction changes are handled by explicit reset or new chat creation in service

  const handleSendMessage = useCallback(async () => {
    if (!input.trim() || isLoading || !apiKeyAvailable) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: input,
      sender: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const aiResponseText = await sendMessageInChat(input, systemInstruction);
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponseText,
        sender: 'ai',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error("Error in AI chat:", error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: "Sorry, I encountered an error. Please try again.",
        sender: 'ai',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [input, isLoading, apiKeyAvailable, systemInstruction]);

  const handleResetChat = () => {
    resetChat();
    setMessages([]);
    // Optionally, provide a fresh system instruction if needed or let user modify.
    // For this component, we'll stick to the initial systemInstruction or allow user to set a new one.
    // If `systemInstruction` was part of state controlled by a textarea, then that would be used.
    // The `startOrGetChat` in `geminiService` will pick up the current `systemInstruction`.
    alert("Chat has been reset. You can start a new conversation with the current system instructions.");
  };

  if (!apiKeyAvailable) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-300 rounded-lg text-yellow-700">
        <h3 className="font-semibold">AI Service Not Available</h3>
        <p>The Gemini API key is not configured. Please ensure the <code>API_KEY</code> environment variable is set to use this feature.</p>
        <p className="mt-2 text-sm">Refer to the setup instructions for more details.</p>
      </div>
    );
  }

  return (
    <div className="border border-neutral-300 rounded-lg shadow-md bg-white">
      <div className="p-4 border-b border-neutral-200">
        <h3 className="text-lg font-semibold text-primary-700">Practice with AI: {moduleTitle}</h3>
        <p className="text-sm text-neutral-600 mt-1">
          Craft prompts to interact with the AI. The current system instruction guides the AI to act as a Biomedical Engineering Assistant.
        </p>
      </div>
      
      <div className="p-4">
        <label htmlFor="systemInstruction" className="block text-sm font-medium text-neutral-700 mb-1">
          System Instruction (AI's Role):
        </label>
        <textarea
          id="systemInstruction"
          value={systemInstruction}
          onChange={(e) => setSystemInstruction(e.target.value)}
          rows={3}
          className="w-full p-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
          placeholder="e.g., You are a helpful assistant..."
        />
        <button
            onClick={handleResetChat}
            title="Reset Chat with new instructions"
            className="mt-2 mb-4 px-3 py-1.5 text-xs font-medium text-white bg-secondary-600 hover:bg-secondary-700 rounded-md flex items-center"
        >
            <ArrowPathIcon className="h-4 w-4 mr-1.5" />
            Apply New Instructions & Reset Chat
        </button>
      </div>

      <div className="h-96 overflow-y-auto p-4 space-y-4 bg-neutral-50 border-t border-b">
        {messages.length === 0 && (
          <div className="text-center text-neutral-500">
            No messages yet. Start by typing a prompt below.
          </div>
        )}
        {messages.map(msg => (
          <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-xl px-4 py-2 rounded-lg shadow ${msg.sender === 'user' ? 'bg-primary-500 text-white' : 'bg-neutral-200 text-neutral-800'}`}>
              <p className="text-sm whitespace-pre-wrap">{msg.text}</p>
              <p className={`text-xs mt-1 ${msg.sender === 'user' ? 'text-primary-200' : 'text-neutral-500'}`}>
                {msg.timestamp.toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-xl px-4 py-2 rounded-lg shadow bg-neutral-200 text-neutral-800">
              <p className="text-sm italic">AI is thinking...</p>
            </div>
          </div>
        )}
      </div>

      <div className="p-4 border-t border-neutral-200">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type your prompt here..."
            className="flex-grow p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none transition-shadow"
            disabled={isLoading || !apiKeyAvailable}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !input.trim() || !apiKeyAvailable}
            className="p-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:bg-neutral-400 transition-colors flex items-center justify-center"
            title="Send message"
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIPromptEngineeringPractice;
