<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medicine: Your Perspective</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0; 
            background-color: #f4f7f6; 
            color: #333; 
            line-height: 1.6; 
        }
        .container { 
            max-width: 800px; 
            margin: 20px auto; 
            padding: 15px 25px 25px 25px;
            background-color: #fff; 
            box-shadow: 0 2px 15px rgba(0,0,0,0.1); 
            border-radius: 8px; 
        }
        h1, h2 { 
            color: #005a87; 
            text-align: center; 
        }
        h1 {
            margin-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .intro-paragraph {
            text-align:center; 
            margin-bottom:30px; 
            font-size: 1.05em;
            color: #555;
        }

        .statement-item { 
            margin-bottom: 25px; 
            padding: 20px; 
            border: 1px solid #e0e0e0; 
            border-radius: 6px; 
            background-color: #fdfdfd; 
        }
        .statement-item p { 
            margin-top: 0; 
            margin-bottom: 15px;
            font-size: 1.05em;
        }
        .slider-container { 
            display: flex; 
            align-items: center; 
            flex-wrap: wrap; 
            gap: 10px;
        }
        .slider-container input[type="range"] { 
            flex-grow: 1; 
            margin-right: 10px; 
            cursor: pointer; 
            -webkit-appearance: none;
            appearance: none;
            height: 10px;
            background: #e9ecef; /* Lighter track background */
            border-radius: 5px;
            outline: none;
        }
        .slider-container input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 22px;
            height: 22px;
            background: #007bff;
            cursor: pointer;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        .slider-container input[type="range"]::-moz-range-thumb {
            width: 18px; /* Firefox thumb size often needs to be smaller if border is included in width */
            height: 18px;
            background: #007bff;
            cursor: pointer;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        .slider-value-label { 
            min-width: 130px; 
            font-weight: bold; 
            color: #005a87;
            text-align: right;
        }

        #submit-button { 
            display: block; 
            width: 100%; 
            padding: 14px; 
            background-color: #28a745; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            font-size: 1.15em; 
            font-weight: bold;
            cursor: pointer; 
            margin-top: 30px; 
            margin-bottom: 30px; 
            transition: background-color 0.2s ease;
        }
        #submit-button:hover { 
            background-color: #218838; 
        }

        #results-container {
            padding-top: 10px;
        }
        #results-container h2 { 
            margin-bottom: 20px; 
        }
        #overall-score-display { 
            font-size: 1.3em; 
            font-weight: bold; 
            text-align: center; 
            margin-bottom: 25px; 
            color: #005a87;
        }
        #score-message { 
            margin-top: 25px; 
            padding: 15px; 
            background-color: #e9f5ff; 
            border-left: 5px solid #007bff; 
            border-radius: 4px;
            font-size: 1.05em;
        }

        #learn-more-container { 
            margin-top: 40px; 
            padding-top: 25px; 
            border-top: 1px solid #e0e0e0; 
        }
        #learn-more-container ul { 
            list-style: disc; 
            padding-left: 20px; 
        }
        #learn-more-container li { 
            margin-bottom: 12px; 
        }
        #learn-more-container a { 
            color: #007bff; 
            text-decoration: none; 
        }
        #learn-more-container a:hover { 
            text-decoration: underline; 
        }

        .bar-graph-container { 
            display: flex; 
            justify-content: space-around; 
            align-items: flex-end; 
            height: 180px; 
            border: 1px solid #ccc; 
            padding: 15px; 
            margin-top: 20px; 
            border-radius: 6px; 
            background-color: #f9f9f9; 
        }
        .bar-wrapper { 
            display: flex; 
            flex-direction: column; 
            align-items: center; 
            text-align: center; 
            width: 30%; 
            height: 100%; 
            justify-content: flex-end; 
        }
        .bar { 
            width: 70%; 
            margin-bottom: 8px; 
            border-radius: 4px 4px 0 0; 
            transition: height 0.7s cubic-bezier(0.25, 0.1, 0.25, 1); 
            position: relative;
        }
        .bar-value {
            position: absolute;
            top: -22px; 
            width: 100%;
            text-align: center;
            font-size: 0.85em;
            color: #333;
            font-weight: bold;
            opacity: 0; 
            transition: opacity 0.5s ease-in 0.6s; /* Delay opacity transition */
        }
        .bar-label { 
            font-size: 0.95em; 
            font-weight: 500;
        }
        #pessimism-bar { background-color: #dc3545; }
        #neutrality-bar { background-color: #ffc107; }
        #optimism-bar { background-color: #28a745; }

        @media (max-width: 700px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .slider-container { 
                flex-direction: column; 
                align-items: stretch; 
            }
            .slider-container input[type="range"] { 
                width: 100%; 
                margin-right: 0; 
                margin-bottom: 8px; 
                order: 2; /* Slider below label */
            }
            .slider-value-label {
                text-align: left;
                margin-bottom: 5px;
                order: 1; /* Label above slider */
            }
            .bar-graph-container { 
                height: 150px; 
            }
            .bar { 
                width: 80%; 
            }
            h1 { font-size: 1.8em; }
            h2 { font-size: 1.5em; }
        }
        @media (max-width: 400px) {
            .bar-label { font-size: 0.8em; }
            .bar-value { font-size: 0.75em; top: -18px;}
            .slider-value-label { font-size: 0.9em; }
            .statement-item p { font-size: 1em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI in Medicine: Your Perspective</h1>
        <p class="intro-paragraph">The rise of Artificial Intelligence (AI) promises to reshape medicine. How do you see its future? Explore common viewpoints and discover your own stance.</p>

        <div id="statements-container">
            <!-- Statements will be dynamically generated here -->
        </div>

        <button id="submit-button">Calculate My Perspective</button>

        <div id="results-container" style="display:none;">
            <h2>Your AI Perspective Profile</h2>
            <p id="overall-score-display">Overall Score: <span id="overall-score-value">0</span>/100</p>
            
            <div class="bar-graph-container">
                <div class="bar-wrapper">
                    <div class="bar" id="pessimism-bar" style="height: 0%;"><span class="bar-value">0%</span></div>
                    <div class="bar-label">Pessimism</div>
                </div>
                <div class="bar-wrapper">
                    <div class="bar" id="neutrality-bar" style="height: 0%;"><span class="bar-value">0%</span></div>
                    <div class="bar-label">Neutrality</div>
                </div>
                <div class="bar-wrapper">
                    <div class="bar" id="optimism-bar" style="height: 0%;"><span class="bar-value">0%</span></div>
                    <div class="bar-label">Optimism</div>
                </div>
            </div>
            <p id="score-message"></p>
        </div>

        <div id="learn-more-container">
            <h2>Learn More About AI in Medicine</h2>
            <p>Explore these resources to deepen your understanding of AI's potential and challenges in the medical field:</p>
            <ul>
                <li><a href="https://www.nature.com/articles/s41591-023-02721-2" target="_blank" rel="noopener noreferrer">Governing health futures 2030 (Nature Medicine)</a></li>
                <li><a href="https://www.who.int/health-topics/artificial-intelligence" target="_blank" rel="noopener noreferrer">WHO - Artificial Intelligence for Health</a></li>
                <li><a href="https://www.ama-assn.org/practice-management/digital/augmented-intelligence-medicine" target="_blank" rel="noopener noreferrer">AMA - Augmented Intelligence in Medicine</a></li>
                <li><a href="https://www.fda.gov/medical-devices/software-medical-device-samd/artificial-intelligence-and-machine-learning-software-medical-device" target="_blank" rel="noopener noreferrer">FDA - AI/ML in Software as a Medical Device</a></li>
                <li><a href="https://www.brookings.edu/articles/how-ai-is-transforming-health-care/" target="_blank" rel="noopener noreferrer">Brookings - How AI is transforming health care</a></li>
                <li><a href="https://hai.stanford.edu/news/ai-revolutionizing-drug-discovery-and-development-what-know" target="_blank" rel="noopener noreferrer">Stanford HAI - AI & Drug Discovery</a></li>
                <li><a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC9998079/" target="_blank" rel="noopener noreferrer">Ethical Implications of AI in Healthcare (Academic Review)</a></li>
                 <li><a href="https://www.geh.org/article/artificial-intelligence-healthcare-benefits-and-challenges" target="_blank" rel="noopener noreferrer">GE Healthcare - AI in Healthcare: Benefits and Challenges</a></li>
            </ul>
        </div>
    </div>

    <script>
        const statementsData = [
            { id: 's1', text: "AI will achieve superhuman accuracy in diagnosing most common diseases within the next decade.", polarity: 1, userValue: 2 },
            { id: 's2', text: "AI will make healthcare significantly more accessible and affordable for people globally.", polarity: 1, userValue: 2 },
            { id: 's3', text: "AI will enable highly personalized treatment plans, leading to substantially better patient outcomes.", polarity: 1, userValue: 2 },
            { id: 's4', text: "AI-driven predictive analytics will be crucial for effectively preventing future pandemics and public health crises.", polarity: 1, userValue: 2 },
            { id: 's5', text: "The widespread adoption of AI in medicine will lead to significant job displacement for many healthcare professionals.", polarity: -1, userValue: 2 },
            { id: 's6', text: "Current AI development in medicine is largely overhyped, and its actual impact will be minimal in the next 5-10 years.", polarity: -1, userValue: 2 },
            { id: 's7', text: "Ethical concerns, data privacy issues, and complex regulatory hurdles will severely limit AI's practical application in frontline medicine.", polarity: -1, userValue: 2 },
            { id: 's8', text: "Over-reliance on AI could lead to a dangerous decline in human doctors' diagnostic skills and critical thinking abilities.", polarity: -1, userValue: 2 }
        ];

        const agreementLevels = [
            "Strongly Disagree", // 0
            "Disagree",          // 1
            "Neutral",           // 2
            "Agree",             // 3
            "Strongly Agree"     // 4
        ];

        const statementsContainer = document.getElementById('statements-container');
        const submitButton = document.getElementById('submit-button');
        const resultsContainer = document.getElementById('results-container');
        const overallScoreValueEl = document.getElementById('overall-score-value');
        const scoreMessageEl = document.getElementById('score-message');
        
        const pessimismBar = document.getElementById('pessimism-bar');
        const neutralityBar = document.getElementById('neutrality-bar');
        const optimismBar = document.getElementById('optimism-bar');

        function renderStatements() {
            statementsData.forEach((statement, index) => {
                const item = document.createElement('div');
                item.classList.add('statement-item');

                const text = document.createElement('p');
                text.textContent = `${index + 1}. ${statement.text}`;
                item.appendChild(text);

                const sliderContainer = document.createElement('div');
                sliderContainer.classList.add('slider-container');
                
                const label = document.createElement('span');
                label.classList.add('slider-value-label');
                label.textContent = agreementLevels[statement.userValue];

                const slider = document.createElement('input');
                slider.type = 'range';
                slider.min = 0;
                slider.max = 4;
                slider.value = statement.userValue;
                slider.setAttribute('aria-label', `Agreement for statement ${index + 1}`);
                slider.dataset.index = index; 
                
                slider.addEventListener('input', (event) => {
                    const newIndex = parseInt(event.target.dataset.index);
                    const newValue = parseInt(event.target.value);
                    statementsData[newIndex].userValue = newValue;
                    // Update the correct label associated with this slider
                    const associatedLabel = event.target.parentElement.querySelector('.slider-value-label');
                    if (associatedLabel) {
                        associatedLabel.textContent = agreementLevels[newValue];
                    }
                });
                
                // Order for flexbox when screen is small (label first)
                sliderContainer.appendChild(label); 
                sliderContainer.appendChild(slider);
                item.appendChild(sliderContainer);
                statementsContainer.appendChild(item);
            });
        }

        function calculateAndShowResults() {
            let totalScoreContribution = 0;
            if (statementsData.length === 0) { // Should not happen with predefined data
                overallScoreValueEl.textContent = "N/A";
                scoreMessageEl.textContent = "No statements available to calculate a score.";
                return;
            }

            statementsData.forEach(statement => {
                const internalValue = statement.userValue - 2; // -2 to +2
                totalScoreContribution += (internalValue * statement.polarity);
            });

            const avgScoreContribution = totalScoreContribution / statementsData.length; // Range: -2 to +2
            const overallScore = Math.round(((avgScoreContribution + 2) / 4) * 100); // Normalize to 0-100

            overallScoreValueEl.textContent = overallScore;

            const pessimismHeight = Math.max(0, 50 - overallScore) * 2;
            const optimismHeight = Math.max(0, overallScore - 50) * 2;
            const neutralityHeight = 100 - Math.abs(overallScore - 50) * 2;

            pessimismBar.style.height = `${pessimismHeight}%`;
            neutralityBar.style.height = `${neutralityHeight}%`;
            optimismBar.style.height = `${optimismHeight}%`;

            pessimismBar.querySelector('.bar-value').textContent = `${Math.round(pessimismHeight)}%`;
            neutralityBar.querySelector('.bar-value').textContent = `${Math.round(neutralityHeight)}%`;
            optimismBar.querySelector('.bar-value').textContent = `${Math.round(optimismHeight)}%`;
            
            document.querySelectorAll('.bar-value').forEach(el => el.style.opacity = '1');

            let message = "";
            if (overallScore <= 20) {
                message = "Your responses suggest a highly skeptical view of AI's role in medicine. It's important to critically assess new technologies, but also to keep an open mind and explore the potential benefits AI might offer under careful development and regulation. Consider learning more about ongoing research and successful implementations.";
            } else if (overallScore <= 40) {
                message = "You seem to lean towards a cautious or concerned view regarding AI in medicine. While valid concerns exist about risks and challenges, many experts also foresee significant advantages. It is important to keep an open mind and explore the potential benefits of AI in medicine. Balancing these perspectives is key; explore resources that discuss both risks and benefits.";
            } else if (overallScore <= 60) {
                message = "Your views appear relatively balanced or neutral about AI in medicine. This is a common stance given the complexities and uncertainties. Continued learning about specific applications and ethical guidelines can help refine your understanding as the field evolves.";
            } else if (overallScore <= 80) {
                message = "You lean towards an optimistic view of AI's potential in medicine. This aligns with hopes for breakthroughs in diagnostics, treatment, and accessibility. It's also important to stay informed about the challenges and ethical considerations to ensure responsible development.";
            } else {
                message = "You express strong optimism about AI transforming medicine for the better. This enthusiasm is shared by many innovators. To help realize this potential, engage with discussions on ethical implementation, patient safety, and ensuring equitable access to AI-driven healthcare.";
            }
            scoreMessageEl.textContent = message;

            statementsContainer.style.display = 'none';
            submitButton.style.display = 'none';
            resultsContainer.style.display = 'block';
            resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        document.addEventListener('DOMContentLoaded', () => {
            renderStatements();
            submitButton.addEventListener('click', calculateAndShowResults);
        });
    </script>
</body>
</html>
