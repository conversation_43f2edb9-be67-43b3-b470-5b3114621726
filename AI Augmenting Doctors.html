<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medicine: Augmenting Doctors</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 30px auto;
            background-color: #fff;
            padding: 25px 40px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e0e0e0;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        h1 {
            font-size: 2.2em;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: inline-block;
            margin: 0 auto 30px auto;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .scenario-description {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 5px solid #3498db;
        }
        .choices button {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1.1em;
            margin: 10px;
            transition: background-color 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .choices button:hover {
            background-color: #2980b9;
        }
        .questions, .feedback, .summary {
            margin-top: 25px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .question-item {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .question-item label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #555;
        }
        .question-item input[type="radio"] {
            margin-right: 8px;
        }
        .question-item .option-label {
            display: inline-block;
            margin-bottom: 5px;
        }
        .submit-btn {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1.1em;
            margin-top: 20px;
            transition: background-color 0.3s ease;
            width: 100%;
        }
        .submit-btn:hover {
            background-color: #229a53;
        }
        .feedback p {
            margin-bottom: 10px;
        }
        .feedback strong {
            color: #e74c3c;
        }
        .feedback .correct-answer {
            color: #27ae60;
            font-weight: bold;
        }
        .progress-tracker {
            margin-top: 40px;
            text-align: center;
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #bdc3c7;
        }
        .progress-bar {
            width: 80%;
            height: 25px;
            background-color: #ddd;
            border-radius: 5px;
            margin: 10px auto;
            overflow: hidden;
            position: relative;
        }
        .progress-fill {
            height: 100%;
            width: 0%;
            background-color: #2ecc71;
            border-radius: 5px;
            transition: width 0.5s ease;
            position: absolute;
            left: 0;
            top: 0;
        }
        .progress-text {
            position: absolute;
            width: 100%;
            text-align: center;
            line-height: 25px;
            color: #333;
            font-weight: bold;
            text-shadow: 0 0 2px rgba(255,255,255,0.7);
        }
        .next-scenario-btn {
            background-color: #f39c12;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1.1em;
            margin-top: 20px;
            transition: background-color 0.3s ease;
            width: 100%;
        }
        .next-scenario-btn:hover {
            background-color: #e67e22;
        }
        .hidden {
            display: none;
        }
        .ai-assist-info {
            background-color: #d6eaf8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 5px solid #3498db;
        }

        /* Specific styles for correctness feedback */
        .feedback-item {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .feedback-item.correct {
            background-color: #e6ffee;
            border: 1px solid #2ecc71;
        }
        .feedback-item.incorrect {
            background-color: #ffebe6;
            border: 1px solid #e74c3c;
        }
        .ethical-discussion {
            background-color: #f2e9fc;
            padding: 20px;
            border-radius: 8px;
            margin-top: 25px;
            border-left: 5px solid #9b59b6;
        }
        .ethical-discussion h3 {
            color: #8e44ad;
            text-align: left;
            margin-bottom: 15px;
        }
        .ethical-discussion ul {
            list-style-type: disc;
            margin-left: 20px;
        }
        .ethical-discussion li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI in Medicine: Augmenting Doctors</h1>

        <div id="welcome-screen">
            <p>Welcome, medical professional! This interactive web app will help you explore the evolving role of Artificial Intelligence in your field. Through a series of scenarios, you'll see how AI can augment your workflow and decision-making, rather than replace your expertise.</p>
            <button onclick="startApp()">Start Learning</button>
        </div>

        <div id="scenario-container" class="hidden">
            <div class="progress-tracker">
                <h2>Your Progress</h2>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                    <span class="progress-text" id="progress-text">0/3 Scenarios Completed</span>
                </div>
            </div>

            <h2 id="scenario-title"></h2>
            <div class="scenario-description" id="scenario-description"></div>

            <div class="choices" id="initial-choice">
                <p>How would you like to proceed?</p>
                <button onclick="chooseOption('without-ai')">Proceed Without AI</button>
                <button onclick="chooseOption('with-ai')">Use AI</button>
            </div>

            <div id="questions-section" class="hidden">
                <div id="ai-assist-display" class="ai-assist-info hidden">
                    <h3>AI Assistant Insights:</h3>
                    <p id="ai-insight-text"></p>
                </div>
                <div class="questions" id="current-questions">
                    </div>
                <button class="submit-btn" onclick="submitAnswers()">Submit Answers</button>
            </div>

            <div id="feedback-section" class="hidden">
                <h2>Feedback on your performance:</h2>
                <div class="feedback" id="feedback-content">
                    </div>
                <div class="summary hidden" id="scenario-summary">
                    <h2>Scenario Summary:</h2>
                    <p id="summary-text"></p>
                    <div class="ethical-discussion">
                        <h3>Ethical Considerations:</h3>
                        <ul id="ethical-list"></ul>
                    </div>
                </div>
                <button class="next-scenario-btn" onclick="nextScenario()">Next Scenario</button>
            </div>
        </div>

        <div id="completion-screen" class="hidden">
            <h2>Congratulations!</h2>
            <p>You have completed all scenarios and gained valuable insights into the role of AI in medicine. Keep exploring and learning!</p>
            <div class="progress-tracker">
                <h2>Final Progress</h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%; background-color: #2ecc71;"></div>
                    <span class="progress-text">3/3 Scenarios Completed</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const scenarios = [
            {
                title: "Scenario 1: Diagnosing a Skin Lesion",
                description: "You are a dermatologist examining a patient with a suspicious skin lesion. You need to determine if it's benign or potentially malignant.",
                questions: [
                    {
                        id: "q1_s1",
                        text: "Which characteristic is most indicative of melanoma?",
                        options: [
                            { text: "Symmetry", value: "a" },
                            { text: "Regular border", value: "b" },
                            { text: "Uniform color", value: "c" },
                            { text: "Asymmetry", value: "d" }
                        ],
                        correct: "d",
                        explanation: "Asymmetry, irregular borders, varied color, diameter over 6mm, and evolution (ABCDEs) are key indicators for melanoma.",
                        ai_hint: "The AI system highlights 'Asymmetry' and 'Irregular Borders' as high-risk features based on image analysis."
                    },
                    {
                        id: "q2_s1",
                        text: "What is the primary diagnostic tool for a suspicious skin lesion?",
                        options: [
                            { text: "Dermoscopy", value: "a" },
                            { text: "Biopsy", value: "b" },
                            { text: "CT Scan", value: "c" },
                            { text: "Blood Test", value: "d" }
                        ],
                        correct: "b",
                        explanation: "A biopsy is essential for definitive diagnosis of a suspicious skin lesion.",
                        ai_hint: "AI recommends a 'Biopsy' for definitive pathological analysis, providing a confidence score for malignancy."
                    }
                ],
                summary_ai: "AI can significantly augment melanoma detection by quickly analyzing dermoscopic images for ABCDE criteria and providing risk scores, guiding the physician towards suspicious lesions. However, it's crucial to remember that AI is a tool to assist, not replace, the final diagnostic decision, which still relies on physician expertise and a definitive biopsy.",
                summary_no_ai: "Without AI, dermatologists rely on their extensive training, experience, and visual inspection using dermoscopy to identify suspicious lesions. This process is effective but can be time-consuming and sometimes prone to human error, especially with subtle cases.",
                ethical_considerations: [
                    "Bias in AI models: If the AI is trained on a dataset predominantly featuring certain skin types, it might perform less accurately on others, leading to misdiagnosis.",
                    "Over-reliance: Physicians might become overly reliant on AI, potentially deskilling their ability to diagnose without the tool.",
                    "Transparency: The 'black box' nature of some AI models can make it difficult to understand why a particular recommendation was made, affecting physician trust and accountability."
                ]
            },
            {
                title: "Scenario 2: Recommending a Treatment Plan for Diabetes",
                description: "You are an endocrinologist consulting a patient with newly diagnosed Type 2 Diabetes Mellitus. You need to recommend an initial treatment plan.",
                questions: [
                    {
                        id: "q1_s2",
                        text: "Which lifestyle modification is universally recommended for Type 2 Diabetes?",
                        options: [
                            { text: "Ketogenic diet only", value: "a" },
                            { text: "Regular physical activity", value: "b" },
                            { text: "High carbohydrate intake", value: "c" },
                            { text: "Skipping meals", value: "d" }
                        ],
                        correct: "b",
                        explanation: "Regular physical activity and a balanced diet are cornerstones of Type 2 Diabetes management.",
                        ai_hint: "The AI system emphasizes 'personalized nutrition and exercise plans' as primary interventions based on patient data, highlighting regular physical activity."
                    },
                    {
                        id: "q2_s2",
                        text: "What is typically the first-line pharmacotherapy for Type 2 Diabetes, assuming no contraindications?",
                        options: [
                            { text: "Insulin", value: "a" },
                            { text: "Sulfonylureas", value: "b" },
                            { text: "Metformin", value: "c" },
                            { text: "GLP-1 receptor agonists", value: "d" }
                        ],
                        correct: "c",
                        explanation: "Metformin is generally the first-line pharmacotherapy due to its efficacy, safety, and cardiovascular benefits.",
                        ai_hint: "AI suggests 'Metformin' as the preferred initial drug, considering the patient's comorbidities and potential drug interactions, and providing a probability of HbA1c reduction."
                    }
                ],
                summary_ai: "AI can process a patient's entire health record, including comorbidities, medication history, and genetic predispositions, to suggest highly personalized and evidence-based treatment plans for diabetes. This can lead to more optimized treatment, but requires the physician to validate the AI's recommendations against their clinical judgment and patient preferences.",
                summary_no_ai: "Developing a treatment plan for diabetes without AI relies on the physician's knowledge of current guidelines, patient history, and a careful assessment of individual factors. This can be complex given the multitude of available medications and patient variability.",
                ethical_considerations: [
                    "Data privacy and security: AI systems for treatment planning require access to vast amounts of sensitive patient data, raising concerns about breaches and misuse.",
                    "Accountability: If an AI-recommended treatment leads to an adverse outcome, determining liability (physician vs. AI developer) can be challenging.",
                    "Patient autonomy and informed consent: Patients need to understand when and how AI is used in their treatment decisions and provide informed consent."
                ]
            },
            {
                title: "Scenario 3: Interpreting a Chest X-ray",
                description: "You are a general practitioner reviewing a chest X-ray for a patient presenting with cough and fever. You need to identify potential pathologies.",
                questions: [
                    {
                        id: "q1_s3",
                        text: "Which finding on a chest X-ray is most consistent with bacterial pneumonia?",
                        options: [
                            { text: "Normal lung fields", value: "a" },
                            { text: "Diffuse interstitial infiltrates", value: "b" },
                            { text: "Lobar consolidation", value: "c" },
                            { text: "Prominent hilar lymph nodes", value: "d" }
                        ],
                        correct: "c",
                        explanation: "Lobar consolidation, appearing as a dense, uniform opacity in a lung lobe, is a classic sign of bacterial pneumonia.",
                        ai_hint: "The AI image analysis tool highlights 'Lobar Consolidation in the right lower lobe' with high confidence, indicating potential bacterial pneumonia."
                    },
                    {
                        id: "q2_s3",
                        text: "A 'butterfly' or 'bat-wing' appearance on a chest X-ray typically suggests what condition?",
                        options: [
                            { text: "Pneumothorax", value: "a" },
                            { text: "Pleural effusion", value: "b" },
                            { text: "Pulmonary edema", value: "c" },
                            { text: "Tuberculosis", value: "d" }
                        ],
                        correct: "c",
                        explanation: "A 'butterfly' or 'bat-wing' pattern is characteristic of pulmonary edema, often due to congestive heart failure.",
                        ai_hint: "AI identifies the 'bat-wing' pattern and suggests 'Pulmonary Edema' as the most likely diagnosis, cross-referencing with the patient's cardiac history."
                    }
                ],
                summary_ai: "AI-powered image analysis tools can rapidly scan and highlight abnormalities on chest X-rays, potentially improving diagnostic speed and accuracy, especially for subtle findings or in high-volume settings. This frees up physicians to focus on complex cases and patient interaction. However, relying solely on AI without human oversight can lead to missed nuanced diagnoses or over-diagnosis.",
                summary_no_ai: "Interpreting chest X-rays without AI requires significant expertise and can be time-consuming. Human radiologists and general practitioners rely on years of training to recognize various patterns and subtle signs of disease, but fatigue and case volume can impact performance.",
                ethical_considerations: [
                    "Algorithmic bias: If the AI is trained on imbalanced datasets, it might perform poorly on certain demographic groups or less common pathologies, leading to disparities in care.",
                    "False positives/negatives: Over-sensitive AI could lead to unnecessary further investigations (false positives), while under-sensitive AI could miss critical conditions (false negatives).",
                    "Regulatory approval and validation: Ensuring AI tools are rigorously tested, validated, and approved for clinical use is paramount to patient safety."
                ]
            }
        ];

        let currentScenarioIndex = 0;
        let scenariosCompleted = 0;
        let useAI = false;
        let userScores = []; // To track performance per scenario (e.g., correct answers)

        const welcomeScreen = document.getElementById('welcome-screen');
        const scenarioContainer = document.getElementById('scenario-container');
        const completionScreen = document.getElementById('completion-screen');

        const scenarioTitle = document.getElementById('scenario-title');
        const scenarioDescription = document.getElementById('scenario-description');
        const initialChoice = document.getElementById('initial-choice');
        const questionsSection = document.getElementById('questions-section');
        const aiAssistDisplay = document.getElementById('ai-assist-display');
        const aiInsightText = document.getElementById('ai-insight-text');
        const currentQuestionsDiv = document.getElementById('current-questions');
        const feedbackSection = document.getElementById('feedback-section');
        const feedbackContent = document.getElementById('feedback-content');
        const scenarioSummary = document.getElementById('scenario-summary');
        const summaryText = document.getElementById('summary-text');
        const ethicalList = document.getElementById('ethical-list');

        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        function startApp() {
            welcomeScreen.classList.add('hidden');
            scenarioContainer.classList.remove('hidden');
            loadScenario();
        }

        function updateProgress() {
            const percentage = (scenariosCompleted / scenarios.length) * 100;
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${scenariosCompleted}/${scenarios.length} Scenarios Completed`;
        }

        function loadScenario() {
            if (currentScenarioIndex >= scenarios.length) {
                scenarioContainer.classList.add('hidden');
                completionScreen.classList.remove('hidden');
                return;
            }

            const scenario = scenarios[currentScenarioIndex];
            scenarioTitle.textContent = scenario.title;
            scenarioDescription.textContent = scenario.description;

            initialChoice.classList.remove('hidden');
            questionsSection.classList.add('hidden');
            feedbackSection.classList.add('hidden');
            aiAssistDisplay.classList.add('hidden');
            currentQuestionsDiv.innerHTML = '';
            feedbackContent.innerHTML = '';
            scenarioSummary.classList.add('hidden');

            updateProgress();
        }

        function chooseOption(option) {
            useAI = (option === 'with-ai');
            initialChoice.classList.add('hidden');
            questionsSection.classList.remove('hidden');
            renderQuestions();
        }

        function renderQuestions() {
            const scenario = scenarios[currentScenarioIndex];
            currentQuestionsDiv.innerHTML = ''; // Clear previous questions

            if (useAI) {
                aiAssistDisplay.classList.remove('hidden');
            } else {
                aiAssistDisplay.classList.add('hidden');
            }

            scenario.questions.forEach((q, qIndex) => {
                const questionItem = document.createElement('div');
                questionItem.classList.add('question-item');
                questionItem.innerHTML = `<label>${qIndex + 1}. ${q.text}</label>`;

                if (useAI && q.ai_hint) {
                    aiInsightText.innerHTML += `<p><strong>AI Insight for Q${qIndex + 1}:</strong> ${q.ai_hint}</p>`;
                } else {
                    aiInsightText.innerHTML = ''; // Clear for "without AI"
                }


                q.options.forEach(option => {
                    const inputId = `${q.id}_${option.value}`;
                    questionItem.innerHTML += `
                        <div>
                            <input type="radio" id="${inputId}" name="${q.id}" value="${option.value}">
                            <label for="${inputId}" class="option-label">${option.text}</label>
                        </div>
                    `;
                });
                currentQuestionsDiv.appendChild(questionItem);
            });
        }

        function submitAnswers() {
            const scenario = scenarios[currentScenarioIndex];
            let correctAnswers = 0;
            feedbackContent.innerHTML = '';

            scenario.questions.forEach((q, qIndex) => {
                const selectedOption = document.querySelector(`input[name="${q.id}"]:checked`);
                const feedbackItem = document.createElement('div');
                feedbackItem.classList.add('feedback-item');

                if (selectedOption) {
                    if (selectedOption.value === q.correct) {
                        correctAnswers++;
                        feedbackItem.classList.add('correct');
                        feedbackItem.innerHTML = `<p><strong>Question ${qIndex + 1}: Correct!</strong> Your choice was '${selectedOption.nextElementSibling.textContent}'.</p>`;
                    } else {
                        feedbackItem.classList.add('incorrect');
                        feedbackItem.innerHTML = `<p><strong>Question ${qIndex + 1}: Incorrect.</strong> Your choice was '${selectedOption.nextElementSibling.textContent}'. The correct answer was: <span class="correct-answer">${q.options.find(opt => opt.value === q.correct).text}</span>.</p>`;
                    }
                    feedbackItem.innerHTML += `<p><em>Explanation:</em> ${q.explanation}</p>`;
                } else {
                    feedbackItem.classList.add('incorrect');
                    feedbackItem.innerHTML = `<p><strong>Question ${qIndex + 1}: Unanswered.</strong> The correct answer was: <span class="correct-answer">${q.options.find(opt => opt.value === q.correct).text}</span>.</p>`;
                    feedbackItem.innerHTML += `<p><em>Explanation:</em> ${q.explanation}</p>`;
                }
                feedbackContent.appendChild(feedbackItem);
            });

            userScores.push({
                scenario: scenario.title,
                correct: correctAnswers,
                total: scenario.questions.length,
                usedAI: useAI
            });

            displayScenarioSummary(correctAnswers, scenario.questions.length);

            questionsSection.classList.add('hidden');
            feedbackSection.classList.remove('hidden');
        }

        function displayScenarioSummary(correct, total) {
            const scenario = scenarios[currentScenarioIndex];
            scenarioSummary.classList.remove('hidden');

            let summaryTextContent = `<p>You answered ${correct} out of ${total} questions correctly.</p>`;

            if (useAI) {
                summaryTextContent += `<p>You chose to use AI for this scenario. ${scenario.summary_ai}</p>`;
            } else {
                summaryTextContent += `<p>You chose to proceed without AI for this scenario. ${scenario.summary_no_ai}</p>`;
            }
            summaryText.innerHTML = summaryTextContent;

            ethicalList.innerHTML = '';
            scenario.ethical_considerations.forEach(ethicalPoint => {
                const li = document.createElement('li');
                li.textContent = ethicalPoint;
                ethicalList.appendChild(li);
            });
        }

        function nextScenario() {
            scenariosCompleted++;
            currentScenarioIndex++;
            loadScenario();
        }

        // Initial setup
        document.addEventListener('DOMContentLoaded', () => {
            updateProgress();
        });
    </script>
</body>
</html>