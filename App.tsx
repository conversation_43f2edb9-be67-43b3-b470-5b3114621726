
import React, { useState, useMemo } from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import ModulePage from './pages/ModulePage';
import { LearningTrack } from './types';
import { modulesData } from './constants';

const App: React.FC = () => {
  const [currentTrack, setCurrentTrack] = useState<LearningTrack>(LearningTrack.BOTH);

  const filteredModules = useMemo(() => {
    if (currentTrack === LearningTrack.BOTH) {
      return modulesData;
    }
    return modulesData.filter(module => module.learningTrack === currentTrack || module.learningTrack === LearningTrack.BOTH);
  }, [currentTrack]);

  return (
    <div className="min-h-screen flex flex-col bg-neutral-50 text-neutral-800">
      <Header currentTrack={currentTrack} setCurrentTrack={setCurrentTrack} />
      <main className="flex-grow container mx-auto px-4 py-8">
        <Routes>
          <Route path="/" element={<HomePage modules={filteredModules} />} />
          <Route path="/module/:moduleId" element={<ModulePage modules={modulesData}/>} />
        </Routes>
      </main>
      <Footer />
    </div>
  );
};

export default App;
