<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Equipment AI Assistant</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
        }

        h1 {
            color: #005a9c;
            text-align: center;
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #005a9c;
        }

        #questionInput {
            width: calc(100% - 22px); /* Account for padding and border */
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }

        #equipmentButtons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        #equipmentButtons button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        #equipmentButtons button:hover {
            background-color: #0056b3;
        }

        .display-area {
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            background-color: #f9f9f9;
        }

        #imageContainer {
            text-align: center;
            margin-bottom: 15px;
        }

        #equipmentImage {
            max-width: 100%;
            height: auto;
            max-height: 200px; /* Control image height */
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        #responseContainer p {
            margin: 8px 0;
            line-height: 1.6;
        }

        #userQuestionDisplay {
            color: #555;
            font-style: italic;
        }
        
        #aiResponsePrefix {
            font-weight: bold;
            color: #005a9c;
        }

        #aiResponseText {
            color: #333;
        }

        #resetButton {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto 0;
            transition: background-color 0.3s ease;
        }

        #resetButton:hover {
            background-color: #c82333;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            #equipmentButtons button {
                font-size: 13px;
                padding: 8px 12px;
            }
            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Medical Equipment AI Assistant</h1>

        <label for="questionInput">Ask me anything about medical equipment!</label>
        <input type="text" id="questionInput" placeholder="e.g., How to troubleshoot a 'low pressure' alarm?">

        <div id="equipmentButtons">
            <button data-equipment="Ventilator">Ventilator</button>
            <button data-equipment="Defibrillator">Defibrillator</button>
            <button data-equipment="ECG Machine">ECG Machine</button>
            <button data-equipment="Vital Signs Monitor">Vital Signs Monitor</button>
            <button data-equipment="Infusion Pump">Infusion Pump</button>
            <button data-equipment="Anesthesia Machine">Anesthesia Machine</button>
        </div>

        <div class="display-area" id="mainDisplayArea" style="display: none;">
            <div id="imageContainer">
                <img id="equipmentImage" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" alt="Equipment Image">
            </div>
            <div id="responseContainer">
                <p id="userQuestionDisplay" style="display:none;"><strong>You asked:</strong> <span id="userQuestionText"></span></p>
                <p id="aiResponsePrefix" style="display:none;"><strong>Medical Equipment AI Assistant:</strong></p>
                <p id="aiResponseText"></p>
            </div>
        </div>
        
        <button id="resetButton">Reset</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const questionInput = document.getElementById('questionInput');
            const equipmentButtons = document.querySelectorAll('#equipmentButtons button');
            const equipmentImage = document.getElementById('equipmentImage');
            const userQuestionDisplay = document.getElementById('userQuestionDisplay');
            const userQuestionText = document.getElementById('userQuestionText');
            const aiResponsePrefix = document.getElementById('aiResponsePrefix');
            const aiResponseText = document.getElementById('aiResponseText');
            const resetButton = document.getElementById('resetButton');
            const mainDisplayArea = document.getElementById('mainDisplayArea');
            const imageContainer = document.getElementById('imageContainer'); // Already part of mainDisplayArea

            const defaultImageSrc = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

            // Function to create a base64 encoded SVG placeholder
            function createSvgPlaceholder(text) {
                const svgString = `<svg viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="200" height="120" fill="#D1E8FF" stroke="#007bff" stroke-width="2" rx="10" ry="10"/><text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="Arial, sans-serif" font-size="18px" fill="#003366">${text}</text></svg>`;
                return "data:image/svg+xml;base64," + btoa(svgString);
            }

            const aiKnowledgeBase = {
                "Ventilator": {
                    image: createSvgPlaceholder("Ventilator"),
                    answers: [
                        "For ventilator 'low pressure' alarms, first check for circuit disconnections, cuff leaks, or leaks around the exhalation valve. Ensure all connections are secure. Refer to manual section 3.2.",
                        "Routine maintenance for ventilators includes daily external cleaning, weekly filter changes (per manufacturer), and calibration checks. Annual preventative maintenance by a certified technician is crucial. See service manual, page 45.",
                        "If the ventilator screen is blank, verify power connection and that the main power switch is ON. Check circuit breakers. If it still doesn't power up, it may need internal component servicing. Contact biomedical engineering."
                    ]
                },
                "Defibrillator": {
                    image: createSvgPlaceholder("Defibrillator"),
                    answers: [
                        "Daily defibrillator checks: ensure battery is charged (indicator green), pads/paddles are connected and not expired, and perform a self-test. Log results. Consult user guide page 8.",
                        "If a defibrillator fails its self-test, remove it from service immediately and label it 'DEFECTIVE'. Common causes are battery issues or internal faults. Do not attempt to use. Refer to troubleshooting, chapter 5.",
                        "After using a defibrillator, clean the unit, restock consumables (pads, paper), charge the battery, and perform a post-use check including a self-test. Document the use and checks."
                    ]
                },
                "ECG Machine": {
                    image: createSvgPlaceholder("ECG Machine"),
                    answers: [
                        "For noisy ECG tracings, check electrode contact (use fresh gel, ensure good skin prep), patient cable integrity, and lead wire connections. Ensure the patient is still and relaxed. See troubleshooting, section 4.",
                        "ECG machine routine care: Clean surface and cables daily. Check paper supply. Verify correct date/time settings. Annually, perform electrical safety tests and calibration. Manual page 22.",
                        "If the ECG machine doesn't print, check if paper is loaded correctly and if the paper tray is closed. Ensure there are no paper jams. If 'paper out' message persists with paper loaded, sensor might be faulty."
                    ]
                },
                "Vital Signs Monitor": {
                    image: createSvgPlaceholder("Vital Signs Monitor"),
                    answers: [
                        "If a Vital Signs Monitor shows erratic SpO2 readings, check sensor placement, patient movement, and perfusion. Ensure the correct sensor type for the patient is used. Refer to user manual, SpO2 section.",
                        "For NIBP (Non-Invasive Blood Pressure) cuff errors on a Vital Signs Monitor, ensure the correct cuff size is used and it's applied snugly. Check for kinks in the tubing. Calibrate NIBP module as per service schedule.",
                        "Clean Vital Signs Monitors daily with approved disinfectant wipes. Pay attention to high-touch areas like screens and buttons. Check cable integrity. Schedule preventative maintenance annually. Chapter 6."
                    ]
                },
                "Infusion Pump": {
                    image: createSvgPlaceholder("Infusion Pump"),
                    answers: [
                        "Common Infusion Pump alarms like 'Occlusion Downstream' mean there's a blockage after the pump. Check IV line for kinks, closed clamps, or issues at the catheter site. Follow troubleshooting guide, alarm codes.",
                        "If an Infusion Pump battery isn't holding charge, it likely needs replacement. Most pumps have user-replaceable batteries, but follow manufacturer instructions. Document battery replacement. See manual section 7.",
                        "Perform daily checks on Infusion Pumps: inspect for physical damage, ensure it powers on, check display, and verify alarm functionality (e.g., door open alarm). Clean thoroughly between patients."
                    ]
                },
                "Anesthesia Machine": {
                    image: createSvgPlaceholder("Anesthesia Machine"),
                    answers: [
                        "Before every Anesthesia Machine use, perform a full pre-use checklist as per manufacturer guidelines. This includes leak tests, ventilator checks, scavenger system, and monitoring functions. Document completion.",
                        "If you suspect a leak in the Anesthesia Machine breathing circuit, systematically check all connections, CO2 absorber, hoses, and the bag. Use the machine's leak test function. Refer to service manual, leak testing.",
                        "Vaporizer issues on an Anesthesia Machine (e.g., incorrect concentration) require immediate attention. Ensure correct agent, proper filling, and that the vaporizer is securely seated. If issues persist, replace vaporizer and send for service."
                    ]
                }
            };

            function handleEquipmentClick(event) {
                const equipmentName = event.target.dataset.equipment;
                const currentQuestion = questionInput.value.trim();
                const equipmentData = aiKnowledgeBase[equipmentName];

                if (equipmentData) {
                    equipmentImage.src = equipmentData.image;
                    equipmentImage.alt = equipmentName + " image";
                    
                    if (currentQuestion) {
                        userQuestionText.textContent = currentQuestion;
                        userQuestionDisplay.style.display = 'block';
                    } else {
                        userQuestionDisplay.style.display = 'none';
                        userQuestionText.textContent = ''; // Clear it if no question
                    }

                    const randomIndex = Math.floor(Math.random() * equipmentData.answers.length);
                    aiResponseText.textContent = equipmentData.answers[randomIndex];
                    aiResponsePrefix.style.display = 'block';
                    
                    mainDisplayArea.style.display = 'block';
                }
            }

            equipmentButtons.forEach(button => {
                button.addEventListener('click', handleEquipmentClick);
            });

            resetButton.addEventListener('click', () => {
                questionInput.value = '';
                userQuestionDisplay.style.display = 'none';
                userQuestionText.textContent = '';
                aiResponsePrefix.style.display = 'none';
                aiResponseText.textContent = '';
                equipmentImage.src = defaultImageSrc;
                equipmentImage.alt = 'Equipment Image';
                mainDisplayArea.style.display = 'none';
            });
        });
    </script>
</body>
</html>
