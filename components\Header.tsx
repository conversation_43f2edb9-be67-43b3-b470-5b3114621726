
import React from 'react';
import { LearningTrack } from '../types';
import TrackSelector from './TrackSelector';
import { Link } from 'react-router-dom';
import { LightBulbIcon } from './icons/ModuleIcons'; // A generic icon for the header

interface HeaderProps {
  currentTrack: LearningTrack;
  setCurrentTrack: (track: LearningTrack) => void;
}

const Header: React.FC<HeaderProps> = ({ currentTrack, setCurrentTrack }) => {
  return (
    <header className="bg-primary-700 text-white shadow-md">
      <div className="container mx-auto px-4 py-4 flex flex-col sm:flex-row justify-between items-center">
        <Link to="/" className="flex items-center space-x-2 text-2xl font-bold hover:text-primary-200 transition-colors">
          <LightBulbIcon className="h-8 w-8 text-yellow-300" />
          <span>AI in Medicine & Engineering LMS</span>
        </Link>
        <TrackSelector currentTrack={currentTrack} onTrackChange={setCurrentTrack} />
      </div>
    </header>
  );
};

export default Header;
