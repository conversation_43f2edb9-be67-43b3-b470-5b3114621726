<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medicine Explorer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .app-container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            text-align: center;
        }

        h1 {
            color: #0077cc;
            margin-bottom: 20px;
        }

        .specializations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 10px;
            border: 2px dashed #0077cc;
            border-radius: 10px;
        }

        .spec-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            border-radius: 10px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            background-color: #e6f3ff;
        }

        .spec-item:hover {
            background-color: #cce7ff;
            transform: translateY(-5px);
        }
        
        .spec-item.selected {
            background-color: #0077cc;
            color: white;
        }

        .spec-item.selected svg {
            filter: brightness(0) invert(1);
        }

        .spec-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 8px;
            transition: transform 0.3s ease;
        }
        .spec-item:hover .spec-icon {
            transform: scale(1.1);
        }

        .spec-name {
            font-size: 0.9em;
            font-weight: bold;
        }

        .info-area {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            min-height: 280px; /* Adjusted to accommodate visuals */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .info-area h2 {
            margin-top: 0;
            color: #005fa3;
            font-size: 1.5em;
        }

        #specializationDescription {
            margin-bottom: 15px;
            font-size: 1.1em;
            line-height: 1.5;
            color: #555;
            min-height: 50px; /* Ensure space even when empty */
        }

        .slider-container {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #eef;
            border-radius: 8px;
        }
        
        .slider-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #005fa3;
        }

        #impactSlider {
            width: 100%;
            cursor: pointer;
        }
        
        .slider-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8em;
            color: #333;
            padding: 0 5px; /* Align with slider thumbs */
        }

        #aiImpactMessage {
            font-size: 1em;
            line-height: 1.4;
            color: #333;
            background-color: #e6f3ff;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            min-height: 60px;
            transition: background-color 0.3s ease;
        }

        #aiVisualDisplay {
            width: 100%;
            height: 100px; /* Fixed height for visuals */
            margin-top: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            background-color: #f0f0f0;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        #aiVisualDisplay svg {
            max-width: 80%;
            max-height: 90px;
            transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
        }
        
        .visual-fade-enter {
            opacity: 0;
            transform: scale(0.8);
        }
        .visual-fade-exit {
            opacity: 1;
            transform: scale(1);
        }


        .disclaimer {
            font-size: 0.8em;
            color: #666;
            margin-top: 30px;
            text-align: center;
            padding: 10px;
            background-color: #eee;
            border-radius: 5px;
        }
        
        /* Initial state message */
        .initial-message {
            color: #777;
            font-style: italic;
        }

        /* Animation for text change */
        .text-update-animation {
            animation: fadeInText 0.5s ease-in-out;
        }

        @keyframes fadeInText {
            from { opacity: 0.5; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Simple robot animation example */
        @keyframes robotArmMove {
            0% { transform: rotate(0deg); }
            50% { transform: rotate(-15deg); }
            100% { transform: rotate(0deg); }
        }
        .animated-robot-arm {
            transform-origin: bottom left;
            animation: robotArmMove 2s infinite ease-in-out;
        }

    </style>
</head>
<body>

    <div class="app-container">
        <h1>AI's Future in Medicine: An Explorer</h1>

        <div class="specializations-grid" id="specializationsGrid">
            <!-- Specialization items will be injected here by JavaScript -->
        </div>

        <div class="info-area" id="infoArea">
            <h2 id="selectedSpecializationName">Select a Specialization</h2>
            <p id="specializationDescription" class="initial-message">Click an icon above to learn more about a medical specialization and AI's potential impact.</p>
            
            <div id="interactiveContent" style="display: none;">
                <div class="slider-container">
                    <label for="impactSlider">Future AI Impact Level:</label>
                    <input type="range" id="impactSlider" min="0" max="2" value="1">
                    <div class="slider-labels">
                        <span>Low</span>
                        <span>Medium</span>
                        <span>High</span>
                    </div>
                </div>
    
                <div id="aiImpactMessage"></div>
                <div id="aiVisualDisplay">
                    <!-- Visuals will be injected here -->
                </div>
            </div>
        </div>

        <div class="disclaimer">
            <p><strong>Disclaimer:</strong> The information and predictions presented here are simplified for educational purposes and are not definitive. The actual impact of AI will be complex and multifaceted. We encourage you to do your own research from reputable sources!</p>
        </div>
    </div>

    <script>
        const specializationsData = [
            {
                id: 'radiology',
                name: 'Radiology',
                icon: `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="3" y1="9" x2="21" y2="9"></line>
                        <line x1="3" y1="15" x2="21" y2="15"></line>
                        <line x1="9" y1="3" x2="9" y2="21"></line>
                        <line x1="15" y1="3" x2="15" y2="21"></line>
                    </svg>`, // Simplified X-Ray film
                description: 'Radiologists use imaging techniques like X-rays, CT scans, and MRIs to diagnose and sometimes treat diseases.',
                aiImpact: [
                    'AI might assist radiologists with minor tasks like report generation or image sorting, improving workflow efficiency slightly.',
                    'AI could significantly help in pre-screening images, highlighting potential anomalies for radiologists, and speeding up diagnostic processes.',
                    'AI could automate much of the routine image analysis, leading to faster diagnoses. Radiologists might focus more on complex cases, interventional procedures, and AI oversight.'
                ],
                aiVisuals: [
                    // Low
                    `<svg viewBox="0 0 100 50"><rect x="10" y="10" width="30" height="30" fill="#0077cc" rx="3"/><text x="25" y="45" font-size="5" text-anchor="middle">MD</text><circle cx="55" cy="25" r="5" fill="#4CAF50"/><text x="55" y="35" font-size="4" text-anchor="middle">AI Chip</text></svg>`,
                    // Medium
                    `<svg viewBox="0 0 100 50"><rect x="5" y="5" width="40" height="40" fill="#aaa" rx="2" stroke="#333" stroke-width="1"/><path d="M25,10 Q30,20 25,30 M20,25 H30" stroke="red" stroke-width="1.5" fill="none"/><circle cx="65" cy="25" r="10" fill="#4CAF50"/><text x="65" y="40" font-size="5" text-anchor="middle">AI Scan</text><text x="85" y="28" font-size="8" >🧑‍⚕️</text></svg>`,
                    // High
                    `<svg viewBox="0 0 100 50"><circle cx="30" cy="25" r="15" fill="#4CAF50"/><text x="30" y="45" font-size="5" text-anchor="middle">AI Dominant</text><rect x="65" y="20" width="15" height="15" fill="#0077cc" rx="2"/><text x="72.5" y="40" font-size="4" text-anchor="middle">MD Oversight</text></svg>`
                ]
            },
            {
                id: 'pathology',
                name: 'Pathology',
                icon: `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2a4 4 0 0 0-4 4v2H6l-1.5 9h15L18 8h-2V6a4 4 0 0 0-4-4z"></path>
                        <circle cx="12" cy="15" r="3"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                    </svg>`, // Simplified Microscope
                description: 'Pathologists examine tissues, cells, and body fluids to identify diseases, often using microscopes and lab tests.',
                aiImpact: [
                    'AI could help digitize and organize slides, or assist with basic cell counting, slightly improving lab efficiency.',
                    'AI algorithms can analyze digital pathology slides to detect patterns or cancerous cells, acting as a "second opinion" or pre-screener for pathologists.',
                    'AI may automate significant portions of slide analysis for common conditions, allowing pathologists to focus on rare diseases, complex interpretations, and research.'
                ],
                aiVisuals: [
                    // Low
                    `<svg viewBox="0 0 100 50"><path d="M20 10 L20 30 L15 35 L15 40 L25 40 L25 35 Z M20 10 L25 5 L30 10" stroke="#0077cc" stroke-width="2" fill="none" /><text x="22" y="47" font-size="4" text-anchor="middle">Pathologist</text><circle cx="55" cy="25" r="5" fill="#4CAF50"/><text x="55" y="35" font-size="4" text-anchor="middle">AI Assist</text></svg>`,
                    // Medium
                    `<svg viewBox="0 0 100 50"><rect x="10" y="10" width="30" height="20" fill="#ddd" stroke="#333" /><circle cx="25" cy="20" r="3" fill="pink" /><circle cx="70" cy="25" r="12" fill="#4CAF50" /><text x="70" y="42" font-size="5" text-anchor="middle">AI Analysis</text><text x="45" y="28" font-size="8" >🔬</text></svg>`,
                    // High
                    `<svg viewBox="0 0 100 50"><circle cx="35" cy="25" r="18" fill="#4CAF50" /><text x="35" y="48" font-size="5" text-anchor="middle">AI Diagnostics</text><path d="M70 20 L70 30 L65 35 L65 40 L75 40 L75 35 Z M70 20 L75 15 L80 20" stroke="#0077cc" stroke-width="1.5" fill="none" /><text x="72" y="47" font-size="3" text-anchor="middle">Pathologist</text></svg>`
                ]
            },
            {
                id: 'dermatology',
                name: 'Dermatology',
                icon: `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <circle cx="12" cy="12" r="3"></circle>
                        <line x1="12" y1="1" x2="12" y2="5"></line>
                        <line x1="1" y1="12" x2="5" y2="12"></line>
                        <line x1="12" y1="19" x2="12" y2="23"></line>
                        <line x1="19" y1="12" x2="23" y2="12"></line>
                    </svg>`, // Simplified skin lesion / target
                description: 'Dermatologists diagnose and treat conditions affecting the skin, hair, and nails, from acne to skin cancer.',
                aiImpact: [
                    'AI tools could help with patient triage based on submitted images or manage appointment scheduling more effectively.',
                    'AI can analyze images of skin lesions for early signs of cancer with high accuracy, assisting dermatologists in diagnosis and decision-making.',
                    'AI-powered apps could allow for widespread remote screening for common skin conditions, with dermatologists managing complex cases or verifying AI findings.'
                ],
                aiVisuals: [
                     // Low
                    `<svg viewBox="0 0 100 50"><path d="M20,15 A15,15 0 0,1 50,15 A15,15 0 0,1 20,15 Z M35,10 L35,0 M35,30 L35,20" stroke="#0077cc" stroke-width="2" fill="#ffebcd" /><text x="35" y="40" font-size="4" text-anchor="middle">Derm</text><circle cx="65" cy="15" r="5" fill="#4CAF50"/><text x="65" y="25" font-size="4" text-anchor="middle">AI Tool</text></svg>`,
                    // Medium
                    `<svg viewBox="0 0 100 50"><path d="M15,25 A10,10 0 1,0 35,25 A10,10 0 1,0 15,25 Z" fill="#ffc0cb" /><circle cx="25" cy="25" r="2" fill="red" /><circle cx="65" cy="25" r="12" fill="#4CAF50" /><text x="65" y="42" font-size="5" text-anchor="middle">AI Skin Scan</text><text x="45" y="28" font-size="8" >🖐️</text></svg>`,
                    // High
                    `<svg viewBox="0 0 100 50"><circle cx="35" cy="25" r="18" fill="#4CAF50" /><text x="35" y="48" font-size="5" text-anchor="middle">AI Derm-Screen</text><path d="M70,22 A8,8 0 0,1 86,22 A8,8 0 0,1 70,22 Z" stroke="#0077cc" stroke-width="1.5" fill="#ffebcd" /><text x="78" y="38" font-size="3" text-anchor="middle">Specialist</text></svg>`
                ]
            },
            {
                id: 'ophthalmology',
                name: 'Ophthalmology',
                icon: `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>`, // Eye
                description: 'Ophthalmologists are medical doctors specializing in eye and vision care, including diseases, injuries, and surgery.',
                aiImpact: [
                    'AI may help with tasks like analyzing visual field tests or optimizing surgical planning parameters.',
                    'AI algorithms can detect eye diseases like diabetic retinopathy or glaucoma from retinal scans, often earlier than human detection.',
                    'AI could conduct widespread, automated eye screenings. Ophthalmologists would focus on surgical interventions and managing complex, AI-flagged cases.'
                ],
                aiVisuals: [
                    // Low
                    `<svg viewBox="0 0 100 50"><path d="M10 25 Q25 15 40 25 Q25 35 10 25 Z" fill="#add8e6" stroke="#0077cc" /><circle cx="25" cy="25" r="5" fill="#333" /><text x="25" y="45" font-size="4" text-anchor="middle">Ophth.</text><circle cx="60" cy="25" r="5" fill="#4CAF50"/><text x="60" y="35" font-size="4" text-anchor="middle">AI Aid</text></svg>`,
                    // Medium
                    `<svg viewBox="0 0 100 50"><path d="M5 25 Q25 10 45 25 Q25 40 5 25 Z" fill="#eee" stroke="#333" /><circle cx="25" cy="25" r="8" fill="lightblue" /><circle cx="25" cy="25" r="3" fill="black" /><text x="85" y="28" font-size="8" >👁️</text><circle cx="65" cy="25" r="10" fill="#4CAF50" /><text x="65" y="40" font-size="5" text-anchor="middle">AI Retinal Scan</text></svg>`,
                    // High
                    `<svg viewBox="0 0 100 50"><circle cx="30" cy="25" r="15" fill="#4CAF50" /><text x="30" y="45" font-size="5" text-anchor="middle">AI Vision Screen</text><path d="M65 25 Q72.5 20 80 25 Q72.5 30 65 25 Z" fill="#add8e6" stroke="#0077cc" stroke-width="1.5"/><circle cx="72.5" cy="25" r="2.5" fill="#333" /><text x="72.5" y="38" font-size="3" text-anchor="middle">Surgeon</text></svg>`
                ]
            },
            {
                id: 'anesthesiology',
                name: 'Anesthesiology',
                icon: `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2z"></path>
                        <path d="M12 8v4l2 2"></path>
                        <path d="M8 20c0-4.418 3.582-8 8-8"></path> <!-- Simplified monitor line -->
                    </svg>`, // Abstract vital monitor / clock
                description: 'Anesthesiologists manage patient pain and vital functions during surgery and other medical procedures.',
                aiImpact: [
                    'AI could help optimize drug dosage calculations based on patient data or improve monitoring alerts.',
                    'AI systems can monitor patient vitals in real-time, predict adverse events (like hypotension), and suggest adjustments to anesthesia delivery.',
                    'While full automation is unlikely soon due to complexity and safety, AI could act as an advanced co-pilot, managing routine aspects of anesthesia under human supervision, freeing anesthesiologists for critical events.'
                ],
                aiVisuals: [
                    // Low
                    `<svg viewBox="0 0 100 50"><polyline points="10,30 15,20 20,30 25,15 30,30 35,25" stroke="#0077cc" fill="none" stroke-width="2"/><text x="22" y="45" font-size="4" text-anchor="middle">Anesth.</text><circle cx="60" cy="25" r="5" fill="#4CAF50"/><text x="60" y="35" font-size="4" text-anchor="middle">AI Monitor</text></svg>`,
                    // Medium
                    `<svg viewBox="0 0 100 50"><polyline points="5,35 15,15 25,30 35,10 45,25" stroke="#0077cc" fill="none" stroke-width="2"/><text x="75" y="28" font-size="8" >🩺</text><circle cx="65" cy="25" r="12" fill="#4CAF50" /><text x="65" y="42" font-size="4" text-anchor="middle">AI Predictive Vitals</text></svg>`,
                    // High
                    `<svg viewBox="0 0 100 50"><circle cx="35" cy="25" r="18" fill="#4CAF50" /><text x="35" y="48" font-size="4" text-anchor="middle">AI Co-Pilot System</text><polyline points="65,30 70,20 75,30 80,15 85,30" stroke="#0077cc" fill="none" stroke-width="1.5"/><text x="75" y="42" font-size="3" text-anchor="middle">Human Lead</text></svg>`
                ]
            },
            {
                id: 'general_practice',
                name: 'General Practice',
                icon: `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        <path d="M12 12h.01"></path> <!-- Stethoscope like elements -->
                        <path d="M17.5 6.5a1.5 1.5 0 0 1 0 3H16v5a2 2 0 0 1-4 0V8H9.5a1.5 1.5 0 1 1 0-3H11V3a1 1 0 0 1 2 0v2h2.5z"></path>
                    </svg>`, // Heart + Stethoscope elements
                description: 'General Practitioners (GPs) provide primary care, diagnose and treat common illnesses, and refer patients to specialists.',
                aiImpact: [
                    'AI can streamline administrative tasks like record keeping, appointment scheduling, and prescription renewals, freeing up GP time.',
                    'AI diagnostic aids can help GPs by suggesting potential diagnoses based on symptoms and patient history, or analyzing basic test results.',
                    'AI could handle initial patient consultations for common issues via chatbots or remote monitoring, with GPs focusing on complex cases, patient relationships, and holistic care management.'
                ],
                aiVisuals: [
                    // Low
                    `<svg viewBox="0 0 100 50"><path d="M15 35C15 25 25 15 35 15S55 25 55 35H15Z M35 15V5" stroke="#0077cc" fill="none" stroke-width="2"/><circle cx="35" cy="5" r="3" fill="#0077cc"/><text x="35" y="48" font-size="4" text-anchor="middle">GP</text><circle cx="70" cy="25" r="5" fill="#4CAF50"/><text x="70" y="35" font-size="4" text-anchor="middle">AI Admin</text></svg>`,
                    // Medium
                    `<svg viewBox="0 0 100 50"><path d="M10 38C10 25 25 12 40 12S70 25 70 38H10Z M40 12V2" stroke="#0077cc" fill="none" stroke-width="2"/><circle cx="40" cy="2" r="3" fill="#0077cc"/><text x="85" y="28" font-size="8" >🧑‍⚕️</text><circle cx="60" cy="25" r="10" fill="#4CAF50" /><text x="60" y="40" font-size="4" text-anchor="middle">AI Diagnostic Aid</text></svg>`,
                    // High
                    `<svg viewBox="0 0 100 50"><circle cx="30" cy="25" r="15" fill="#4CAF50" /><text x="30" y="45" font-size="4" text-anchor="middle">AI Triage/Basic Care</text><path d="M65 35C65 28 70 20 77.5 20S90 28 90 35H65Z M77.5 20V13" stroke="#0077cc" fill="none" stroke-width="1.5"/><circle cx="77.5" cy="13" r="2" fill="#0077cc"/><text x="77.5" y="45" font-size="3" text-anchor="middle">GP Complex Care</text></svg>`
                ]
            }
        ];

        const specializationsGrid = document.getElementById('specializationsGrid');
        const selectedSpecializationName = document.getElementById('selectedSpecializationName');
        const specializationDescription = document.getElementById('specializationDescription');
        const impactSlider = document.getElementById('impactSlider');
        const aiImpactMessage = document.getElementById('aiImpactMessage');
        const aiVisualDisplay = document.getElementById('aiVisualDisplay');
        const interactiveContent = document.getElementById('interactiveContent');
        
        let currentSpecialization = null;

        function displaySpecializations() {
            specializationsData.forEach(spec => {
                const item = document.createElement('div');
                item.classList.add('spec-item');
                item.dataset.id = spec.id;
                item.innerHTML = `
                    <div class="spec-icon">${spec.icon}</div>
                    <span class="spec-name">${spec.name}</span>
                `;
                item.addEventListener('click', () => selectSpecialization(spec));
                specializationsGrid.appendChild(item);
            });
        }

        function selectSpecialization(spec) {
            currentSpecialization = spec;

            // Update selected item styling
            document.querySelectorAll('.spec-item').forEach(el => el.classList.remove('selected'));
            document.querySelector(`.spec-item[data-id="${spec.id}"]`).classList.add('selected');
            
            selectedSpecializationName.textContent = spec.name;
            specializationDescription.textContent = spec.description;
            specializationDescription.classList.remove('initial-message');
            specializationDescription.classList.add('text-update-animation');
            
            interactiveContent.style.display = 'block';
            impactSlider.value = 1; // Default to Medium
            updateImpactInfo();

            // Remove animation class after it plays
            setTimeout(() => specializationDescription.classList.remove('text-update-animation'), 500);
        }

        function updateImpactInfo() {
            if (!currentSpecialization) return;

            const impactLevel = parseInt(impactSlider.value); // 0: Low, 1: Medium, 2: High
            
            aiImpactMessage.textContent = currentSpecialization.aiImpact[impactLevel];
            aiImpactMessage.classList.add('text-update-animation');

            // Visual update with simple fade
            aiVisualDisplay.classList.add('visual-fade-enter');
            aiVisualDisplay.innerHTML = ''; // Clear previous visual

            setTimeout(() => {
                aiVisualDisplay.innerHTML = currentSpecialization.aiVisuals[impactLevel];
                aiVisualDisplay.classList.remove('visual-fade-enter');
                 // Re-trigger layout for transition if SVG is complex
                aiVisualDisplay.offsetHeight;
            }, 50); // Short delay for fade out effect before new content

            // Remove animation class after it plays
            setTimeout(() => aiImpactMessage.classList.remove('text-update-animation'), 500);
        }

        impactSlider.addEventListener('input', updateImpactInfo);

        // Initialize
        displaySpecializations();

    </script>
</body>
</html>
