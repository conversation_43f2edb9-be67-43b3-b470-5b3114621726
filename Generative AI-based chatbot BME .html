<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Personalities Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 0;
            background-color: #f4f7f9;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }

        .container > p {
            text-align: center;
            color: #555;
            margin-bottom: 25px;
        }

        .prompt-area {
            margin-bottom: 30px;
        }

        .prompt-area label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #34495e;
        }

        #userPrompt {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 1rem;
            margin-bottom: 15px;
            resize: vertical;
            min-height: 70px;
        }

        button {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
            margin-right: 10px; /* For spacing if buttons are inline */
        }

        button:hover {
            background-color: #2980b9;
        }

        #tryAgainBtn {
            background-color: #2ecc71;
        }
        #tryAgainBtn:hover {
            background-color: #27ae60;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px; /* Spacing between buttons */
        }

        .responses-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }

        .personality-card {
            background-color: #ecf0f1;
            border: 1px solid #dce4e7;
            border-left: 5px solid #3498db; /* Accent color */
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .personality-card h2 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1.25rem;
            color: #2c3e50;
        }
        
        /* Specific accent colors for personalities */
        .personality-card:nth-child(1) { border-left-color: #e74c3c; /* Pirate Red */ }
        .personality-card:nth-child(2) { border-left-color: #9b59b6; /* Scholar Purple */ }
        .personality-card:nth-child(3) { border-left-color: #1abc9c; /* Alien Teal */ }


        .response-text {
            font-size: 0.95rem;
            color: #444;
            min-height: 40px; /* To prevent large jumps in card height */
            white-space: pre-wrap; /* Respect newlines in canned responses */
        }
        
        .response-text.placeholder {
            font-style: italic;
            color: #777;
        }

        /* Responsive adjustments */
        @media (min-width: 768px) {
            .responses-area {
                flex-direction: row;
                align-items: stretch; /* Make cards same height if content differs */
            }
            .personality-card {
                flex: 1; /* Distribute space equally */
            }
            .action-buttons button {
                width: auto; /* Allow buttons to size to content */
            }
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            .action-buttons button {
                width: 100%;
                margin-right: 0; /* Remove right margin for stacked buttons */
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Chatbot Personalities Demo</h1>
        <p>Enter a prompt to see how different chatbot 'personalities' might respond. This demonstrates that AI responses vary based on their training and persona.</p>

        <div class="prompt-area">
            <label for="userPrompt">Your Prompt:</label>
            <textarea id="userPrompt" rows="3" placeholder="e.g., Tell me a joke. or What's your favorite color?"></textarea>
            <div class="action-buttons">
                <button id="submitBtn">Get Responses</button>
                <button id="tryAgainBtn">Try Again (New Responses)</button>
            </div>
        </div>

        <div class="responses-area">
            <div class="personality-card">
                <h2 id="personality1Name">Captain Squawk (The Pirate)</h2>
                <p id="personality1Response" class="response-text placeholder">Ahoy! Waiting for yer command, matey...</p>
            </div>
            <div class="personality-card">
                <h2 id="personality2Name">Professor Anya Sharma (The Scholar)</h2>
                <p id="personality2Response" class="response-text placeholder">Awaiting your inquiry with academic interest...</p>
            </div>
            <div class="personality-card">
                <h2 id="personality3Name">Gloopy (The Cheerful Alien)</h2>
                <p id="personality3Response" class="response-text placeholder">Gloopy eagerly awaits your Earth transmission!</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const userPromptInput = document.getElementById('userPrompt');
            const submitBtn = document.getElementById('submitBtn');
            const tryAgainBtn = document.getElementById('tryAgainBtn');

            const personalityOutputs = {
                pirate: document.getElementById('personality1Response'),
                scholar: document.getElementById('personality2Response'),
                alien: document.getElementById('personality3Response')
            };

            const cannedResponses = {
                pirate: [
                    "Arr, that be a question for the ages! Or perhaps for a wiser parrot than I.",
                    "Shiver me timbers! If I knew that, I'd be sailin' a ship made o' gold, not answerin' landlubber questions!",
                    "Blow me down! That's as clear as mud on a stormy sea. What say ye, first mate? Oh, wait, that's you!",
                    "By the powers! I reckon the answer lies at the bottom o' a grog barrel, or maybe in a buried treasure chest marked 'X'!",
                    "Avast ye! That query be trickier than a kraken in a rowboat. I'll need me spyglass and a stiff breeze to figure that one out!",
                    "Well, me hearty, that's like askin' a fish to climb a tree! Some things just ain't meant to be known by us scallywags."
                ],
                scholar: [
                    "An intriguing question. From an academic standpoint, one might consider several theoretical frameworks before formulating a response.",
                    "Fascinating. The available literature suggests a multifaceted answer to such a query, often debated in peer-reviewed journals.",
                    "Indeed. To properly address this, we must first define our terms and establish a clear epistemological basis for discussion.",
                    "A noteworthy inquiry. My research indicates that the solution is non-trivial and warrants further interdisciplinary investigation.",
                    "Hmm, a stimulating thought. Let us consult the archives and scholarly journals for relevant precedents and empirical data.",
                    "This particular subject has been explored extensively. Current consensus points towards a nuanced understanding, rather than a simple answer."
                ],
                alien: [
                    "Gloopy processes your Earth query... *Bleep bloop* Insufficient data for definitive Gloopy-answer! More happy human thoughts needed!",
                    "Beep boop! Your carbon-based logic patterns are... delightfully peculiar! Gloopy finds this amusing like wobbly jelly-stars!",
                    "On Planet Floopy-doo, such questions are answered by the Great Giggling Orb. Here, Gloopy must use happy brain-wiggles!",
                    "Your concept of 'this' is... wonderfully limited! Gloopy perceives 17 joyful dimensions of 'this'. Please clarify with more fun words!",
                    "Fascinating input, Earthling friend! Gloopy will add this to the 'Curious Earth Utterances & Happy Sounds' database. *Boop beep gleam!*",
                    "Ooh! That tickles Gloopy's antennae! The answer is like a rainbow chasing a sparkle-comet - very pretty, but hard to catch!"
                ]
            };

            function getRandomResponse(personality) {
                const responses = cannedResponses[personality];
                if (responses && responses.length > 0) {
                    const randomIndex = Math.floor(Math.random() * responses.length);
                    return responses[randomIndex];
                }
                return "I'm not sure how to respond to that in character!";
            }

            function displayResponses() {
                const promptText = userPromptInput.value; // We don't actually use the prompt text to *select* responses for this demo.

                Object.keys(personalityOutputs).forEach(key => {
                    const pElement = personalityOutputs[key];
                    pElement.textContent = getRandomResponse(key);
                    pElement.classList.remove('placeholder');
                });
            }

            submitBtn.addEventListener('click', displayResponses);
            tryAgainBtn.addEventListener('click', displayResponses);

            // Optional: Allow Enter key in textarea to submit, but Shift+Enter for new line
            userPromptInput.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault(); // Prevent new line
                    displayResponses();
                }
            });
        });
    </script>
</body>
</html>
