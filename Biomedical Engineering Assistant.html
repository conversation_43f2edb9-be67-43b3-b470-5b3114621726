<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Biomedical Engineering Assistant</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #343a40;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            background: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #0056b3; /* A professional blue */
            margin-bottom: 10px;
        }

        p.description {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.1em;
            color: #495057;
        }

        .input-area {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 30px;
        }

        .input-area label {
            font-weight: bold;
            font-size: 1em;
            color: #343a40;
        }

        #deviceInput {
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        #deviceInput:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        #submitButton {
            padding: 12px 18px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            transition: background-color 0.2s ease-in-out;
        }

        #submitButton:hover {
            background-color: #0056b3;
        }

        #resultsArea {
            margin-top: 20px;
        }

        .device-info h2 {
            color: #0056b3;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
            margin-top: 0;
            margin-bottom: 15px;
        }

        .device-info h3 {
            color: #17a2b8; /* A teal color for subheadings */
            margin-top: 20px;
            margin-bottom: 8px;
            font-size: 1.15em;
        }

        .device-info ul {
            list-style-type: disc;
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .device-info ul li {
            margin-bottom: 5px;
            color: #495057;
        }

        .error {
            color: #dc3545; /* Bootstrap danger color */
            font-weight: bold;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px 15px;
            border-radius: 5px;
            text-align: center;
        }

        /* Responsive adjustments */
        @media (min-width: 768px) {
            .input-area {
                flex-direction: row;
                align-items: flex-end; /* Align items to bottom for better baseline with button */
                gap: 15px;
            }

            .input-area label {
                margin-bottom: 12px; /* Align text with input text */
                 white-space: nowrap; /* Prevent label from wrapping */
            }

            #deviceInput {
                flex-grow: 1;
                width: auto; /* Allow flex-grow to determine width */
            }

            #submitButton {
                width: auto; /* Adjust button width to content */
                flex-shrink: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Biomedical Engineering Assistant</h1>
        <p class="description">This web app provides quick access to relevant technical information for biomedical engineers.</p>

        <div class="input-area">
            <label for="deviceInput">Enter Medical Device Name:</label>
            <input type="text" id="deviceInput" placeholder="E.g., Ventilator, Infusion Pump...">
            <button id="submitButton" type="button">Search</button>
        </div>

        <div id="resultsArea" aria-live="polite">
            <!-- Results will be displayed here -->
        </div>
    </div>

    <script>
        const medicalDevicesData = [
            {
                name: "Ventilator",
                failureModes: ["Oxygen sensor failure", "Power supply malfunction", "Blocked air filter or tubing"],
                maintenanceProcedures: ["Calibrate oxygen sensor regularly", "Check power connections and battery backup", "Replace air filter and inspect tubing as per schedule"],
                partsRequired: ["Oxygen sensor", "Power supply unit (PSU)", "Air filter kit, breathing circuits"]
            },
            {
                name: "Infusion Pump",
                failureModes: ["Occlusion alarm (blockage detected)", "Battery failure or short operational life", "Free-flow (uncontrolled fluid delivery)"],
                maintenanceProcedures: ["Perform occlusion alarm test routinely", "Regular battery performance check and replacement", "Calibrate flow rate accuracy annually or per manufacturer"],
                partsRequired: ["Proprietary IV tubing set", "Rechargeable battery pack", "Pressure sensor module"]
            },
            {
                name: "Defibrillator",
                failureModes: ["Battery depleted or faulty", "Paddle/Pad connectivity issue or expiry", "Capacitor unable to charge or hold charge"],
                maintenanceProcedures: ["Daily battery check and device self-test", "Inspect paddles/pads for damage, gel, and expiry date", "Periodic energy delivery test using a defibrillator analyzer"],
                partsRequired: ["Battery pack (often model-specific)", "Disposable electrode pads (adult/pediatric)", "High-voltage capacitor"]
            },
            {
                name: "ECG Machine",
                failureModes: ["Noisy or artifact-laden signal", "Lead wire breakage or poor electrode contact", "Printer malfunction or paper jam"],
                maintenanceProcedures: ["Clean lead wires and patient cable connectors", "Inspect patient cable for frays or damage", "Calibrate signal gain and check paper supply/path"],
                partsRequired: ["Patient cable assembly (trunk and leads)", "ECG recording paper (thermal)", "Set of reusable or disposable electrodes"]
            },
            {
                name: "Patient Monitor",
                failureModes: ["Display screen issues (flickering, dead pixels, no image)", "Sensor malfunction (SpO2, NIBP, Temperature)", "False alarms or alarm system failure"],
                maintenanceProcedures: ["Calibrate NIBP module annually", "Check and clean sensor connections (SpO2, ECG, Temp)", "Perform comprehensive alarm system tests"],
                partsRequired: ["SpO2 finger sensor/probe", "NIBP cuff and hose", "Replacement LCD display panel or backlight"]
            },
            {
                name: "Anesthesia Machine",
                failureModes: ["Gas leak in breathing circuit or supply lines", "Vaporizer malfunction (incorrect anesthetic concentration)", "Ventilator component failure (bellows, drive mechanism, valves)"],
                maintenanceProcedures: ["Perform pre-use machine checklist including leak test", "Calibrate vaporizers periodically (agent-specific)", "Inspect and replace CO2 absorbent granules"],
                partsRequired: ["O-rings and seals kit for circuits/vaporizers", "Agent-specific vaporizer (Sevoflurane, Isoflurane, etc.)", "Flow sensor assembly, O2 cell"]
            },
            {
                name: "Electrosurgical Unit",
                failureModes: ["No or low power output", "Patient return electrode monitoring (REM/CQM) alarm", "Handpiece or footswitch malfunction"],
                maintenanceProcedures: ["Verify power output accuracy with ESU analyzer", "Inspect patient return electrode, cable, and connector", "Check handpiece and footswitch integrity and connections"],
                partsRequired: ["Electrosurgical pencil/handpiece (monopolar/bipolar)", "Patient return electrodes (dispersive pads)", "Footswitch assembly (mono/bipolar)"]
            },
            {
                name: "Medical Suction Pump",
                failureModes: ["Low or no suction power", "Motor failure or overheating", "Clogged bacterial filter or full collection canister"],
                maintenanceProcedures: ["Check suction level against gauge and regulator function", "Clean or replace bacterial/hydrophobic filter regularly", "Empty and clean collection canister after each use or daily"],
                partsRequired: ["Suction collection canister and lid", "Hydrophobic/bacterial filter", "Suction tubing and connectors"]
            },
            {
                name: "Pulse Oximeter",
                failureModes: ["Inaccurate SpO2 or pulse rate readings", "Sensor detection issues (no sensor, poor signal)", "Low battery or power supply problems"],
                maintenanceProcedures: ["Clean sensor probe (finger clip, Y-sensor) regularly", "Verify accuracy against a functional tester or simulator", "Check battery health and charging system"],
                partsRequired: ["Reusable or disposable SpO2 sensor probe", "Replacement batteries (if applicable)", "Power adapter or charging cable"]
            },
            {
                name: "Hospital Bed",
                failureModes: ["Motor failure (head, foot, height adjustment)", "Control panel or hand pendant unresponsive", "Caster lock mechanism faulty or broken"],
                maintenanceProcedures: ["Lubricate moving parts and actuators as per manual", "Inspect electrical wiring, connections, and strain reliefs", "Test all bed functions, safety features (CPR release, brakes)"],
                partsRequired: ["Actuator motor (specific to function and bed model)", "Hand pendant control unit", "Locking caster wheel assembly"]
            }
        ];

        const deviceInput = document.getElementById('deviceInput');
        const submitButton = document.getElementById('submitButton');
        const resultsArea = document.getElementById('resultsArea');

        const LEVENSHTEIN_THRESHOLD = 2; // Max allowed distance for a typo match

        function levenshteinDistance(s1, s2) {
            s1 = s1.toLowerCase();
            s2 = s2.toLowerCase();

            const d = []; // 2D array for dynamic programming

            for (let i = 0; i <= s1.length; i++) {
                d[i] = [i];
            }
            for (let j = 0; j <= s2.length; j++) {
                d[0][j] = j;
            }

            for (let j = 1; j <= s2.length; j++) {
                for (let i = 1; i <= s1.length; i++) {
                    const cost = (s1[i - 1] === s2[j - 1]) ? 0 : 1;
                    d[i][j] = Math.min(
                        d[i - 1][j] + 1,        // deletion
                        d[i][j - 1] + 1,        // insertion
                        d[i - 1][j - 1] + cost  // substitution
                    );
                }
            }
            return d[s1.length][s2.length];
        }

        function findDevice(userInput) {
            const normalizedInput = userInput.trim().toLowerCase();
            if (!normalizedInput) return null;

            let bestMatchDevice = null;
            let smallestDistance = Infinity;

            for (const device of medicalDevicesData) {
                const deviceNameLower = device.name.toLowerCase();

                // Prioritize exact match
                if (deviceNameLower === normalizedInput) {
                    return device;
                }

                const distance = levenshteinDistance(normalizedInput, deviceNameLower);

                if (distance < smallestDistance) {
                    smallestDistance = distance;
                    bestMatchDevice = device;
                }
            }

            if (bestMatchDevice && smallestDistance <= LEVENSHTEIN_THRESHOLD) {
                return bestMatchDevice;
            }

            return null; // No suitable match found
        }

        function displayDeviceInfo(device) {
            resultsArea.innerHTML = ''; // Clear previous results

            const infoDiv = document.createElement('div');
            infoDiv.className = 'device-info';

            const title = document.createElement('h2');
            title.textContent = `${device.name} Information`;
            infoDiv.appendChild(title);

            function createSection(titleText, items) {
                if (items && items.length > 0) {
                    const sectionTitle = document.createElement('h3');
                    sectionTitle.textContent = titleText;
                    infoDiv.appendChild(sectionTitle);

                    const ul = document.createElement('ul');
                    items.forEach(item => {
                        const li = document.createElement('li');
                        li.textContent = item;
                        ul.appendChild(li);
                    });
                    infoDiv.appendChild(ul);
                }
            }

            createSection('Common Failure Modes:', device.failureModes);
            createSection('Common Maintenance Procedures:', device.maintenanceProcedures);
            createSection('Common Parts Required for Repair:', device.partsRequired);

            resultsArea.appendChild(infoDiv);
        }

        function displayError(message) {
            resultsArea.innerHTML = ''; // Clear previous results
            const errorP = document.createElement('p');
            errorP.className = 'error';
            errorP.textContent = message;
            resultsArea.appendChild(errorP);
        }

        function handleSubmit() {
            const userInput = deviceInput.value;
            if (!userInput.trim()) {
                displayError("Please enter a medical device name.");
                return;
            }

            const foundDevice = findDevice(userInput);

            if (foundDevice) {
                displayDeviceInfo(foundDevice);
            } else {
                displayError(`Device "${userInput}" not found. Please check the spelling or try another device name from our list.`);
            }
        }

        submitButton.addEventListener('click', handleSubmit);
        deviceInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault(); // Prevent form submission if it were in a form
                handleSubmit();
            }
        });

    </script>
</body>
</html>
