<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI in Medical Engineering</title>
    <style>
        /* Global styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background-color: #F8F9FA; /* Light grey page background */
            color: #333333; /* Dark grey text */
            line-height: 1.6;
        }

        header {
            background-color: #005A9C; /* Primary Blue */
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 600;
        }

        #tabs {
            display: flex;
            justify-content: center;
            background-color: #004C80; /* Slightly darker strip for tabs */
            padding: 0 10px; 
            border-bottom: 1px solid #003D6B; /* Darker border at bottom of tab bar */
        }

        .tab-button {
            background-color: transparent; 
            color: #E6F0F7; 
            border: 1px solid transparent; 
            border-bottom: none;
            padding: 12px 25px;
            margin: 0; /* Removed margin to make them touch */
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: background-color 0.3s, color 0.3s;
            border-radius: 5px 5px 0 0;
        }

        .tab-button:hover {
            background-color: #003D6B; 
            color: white;
        }

        .tab-button.active {
            background-color: #F8F9FA; 
            color: #005A9C; 
            font-weight: bold;
            border-color: #003D6B #003D6B #F8F9FA; /* Left, Right, Bottom (to blend) */
            position: relative;
            top: 1px; /* Lifts the tab to appear connected */
        }

        main {
            padding: 20px;
        }

        .content-section {
            display: none; /* Hidden by default */
            background-color: white;
            padding: 25px;
            margin: 0 auto 20px auto;
            max-width: 800px;
            border: 1px solid #003D6B; /* Border matching tab bar */
            border-top: none; /* Top border handled by active tab */
            border-radius: 0 0 8px 8px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }

        .content-section.active {
            display: block; /* Show active section */
        }

        .content-section h2 {
            color: #005A9C; 
            margin-top: 0;
            border-bottom: 2px solid #E6F0F7; 
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .content-section h3 {
            color: #004C80; 
            margin-top: 25px;
            margin-bottom: 10px;
            font-size: 1.4em;
        }
        .content-section h4 {
            color: #003D6B; 
            margin-top: 15px;
            margin-bottom: 5px;
            font-size: 1.2em;
        }

        .content-section p, .content-section li {
            margin-bottom: 15px;
            color: #555; 
        }

        .example {
            background-color: #E6F0F7; 
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #005A9C; 
            border-radius: 4px;
        }
        .example p {
            margin-bottom: 0;
            color: #333;
        }
        .example p strong {
            color: #005A9C;
        }

        /* Quiz specific styles */
        .quiz-question {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #D1D1D1; 
            border-radius: 6px;
            background-color: #fff; 
        }
        .quiz-question p strong { /* Question text */
            color: #005A9C;
            font-size: 1.1em;
            display: block;
            margin-bottom: 10px;
        }
        .options label {
            display: block;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s, border-color 0.2s;
            border: 1px solid #eee; 
        }
        .options label:hover {
            background-color: #E6F0F7; 
            border-color: #005A9C;
        }
        .options input[type="radio"] {
            margin-right: 10px;
            vertical-align: middle;
        }

        button#checkAnswersBtn {
            background-color: #28a745; 
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.05em;
            font-weight: 500;
            transition: background-color 0.3s;
            display: block; 
            margin: 20px auto 0 auto; 
        }
        button#checkAnswersBtn:hover {
            background-color: #218838; 
        }

        #quizResults {
            margin-top: 30px;
            padding: 10px 0 0 0; /* Reduced top padding, handled by first p */
        }
        #quizResults p {
            margin-bottom: 12px;
            padding: 12px 15px;
            border-radius: 4px;
            font-size: 0.95em;
        }
        #quizResults p.correct {
            background-color: #d4edda; 
            color: #155724;
            border-left: 4px solid #28a745;
        }
        #quizResults p.incorrect {
            background-color: #f8d7da; 
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        #quizResults p.unanswered {
            background-color: #fff3cd; 
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        #quizResults .final-score {
            font-weight: bold;
            font-size: 1.2em;
            color: #005A9C;
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            border-top: 1px solid #D1D1D1;
            background-color: #E6F0F7;
            border-radius: 4px;
        }
        #quizResults .final-score strong {
            font-size: 1.2em;
        }

        footer {
            text-align: center;
            padding: 20px;
            background-color: #333333; 
            color: #ccc; 
            font-size: 0.9em;
            margin-top: 40px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            header h1 {
                font-size: 1.8em;
            }
            #tabs {
                flex-direction: column; 
                padding: 0; 
            }
            .tab-button {
                width: 100%; 
                margin: 0; 
                border-radius: 0; 
                border-bottom: 1px solid #003D6B; 
                padding: 15px;
                box-sizing: border-box; /* Ensure padding doesn't add to width */
            }
            .tab-button.active {
                border-left: none;
                border-right: none;
                border-top: 1px solid #003D6B;
                border-bottom: 3px solid #005A9C; /* Stronger indicator on mobile */
                top: 0; /* Reset top positioning for stacked tabs */
            }
            .tab-button:last-child {
                border-bottom: none; 
            }

            main {
                padding: 10px;
            }
            .content-section {
                margin-left: 0;
                margin-right: 0;
                padding: 20px;
                border-radius: 0; 
                border-left: none;
                border-right: none;
            }
            .content-section.active { /* Ensure active content section has top border if tabs are stacked */
                 border-top: 1px solid #003D6B;
            }
            .content-section h2 {
                font-size: 1.6em;
            }
            button#checkAnswersBtn {
                width: 80%;
            }
        }

        @media (max-width: 480px) {
            header h1 {
                font-size: 1.5em;
            }
            .tab-button {
                font-size: 1em; 
                padding: 12px;
            }
            .content-section h2 {
                font-size: 1.4em;
            }
            .content-section h3 {
                font-size: 1.2em;
            }
            .content-section h4 {
                font-size: 1.1em;
            }
            .options label {
                font-size: 0.95em;
                padding: 8px;
            }
            button#checkAnswersBtn {
                width: 90%;
                font-size: 1em;
            }
            .quiz-question p strong {
                font-size: 1.05em;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>AI in Medical Engineering</h1>
        <nav id="tabs">
            <button class="tab-button" onclick="showSection('whatIsAI', this)">What is AI?</button>
            <button class="tab-button" onclick="showSection('howAIHelps', this)">How AI Can Help Engineers</button>
            <button class="tab-button" onclick="showSection('testYourself', this)">Test Yourself!</button>
        </nav>
    </header>

    <main>
        <section id="whatIsAI" class="content-section">
            <h2>What is AI?</h2>
            <p>Artificial intelligence involves creating computer systems that can perform tasks that usually require human intelligence. These tasks include problem-solving, learning, and decision-making.</p>
            
            <h3>Related Concepts:</h3>
            <h4>Machine Learning (ML)</h4>
            <p>Machine learning is a type of AI where systems learn from data without being explicitly programmed. Think of it like teaching a child by showing them examples. Instead of writing specific instructions for every possible scenario, you feed the system data, and it learns patterns and makes predictions or decisions based on that data.</p>
            
            <h4>Deep Learning (DL)</h4>
            <p>Deep learning is a more advanced part of machine learning that uses complex structures called neural networks, inspired by the human brain, to learn from vast amounts of data. These neural networks have multiple layers (hence "deep"), allowing them to learn intricate patterns and representations. It's particularly powerful for tasks like image recognition, natural language processing, and speech recognition.</p>
        </section>

        <section id="howAIHelps" class="content-section">
            <h2>How AI Can Help Medical Engineers</h2>
            <div class="example">
                <p><strong>1. Data Analysis & Prediction:</strong> AI can analyze large amounts of medical data to identify patterns and predict potential problems with medical equipment.</p>
            </div>
            <div class="example">
                <p><strong>2. Enhanced Device Design:</strong> AI can help engineers design new medical devices that are more effective and efficient.</p>
            </div>
            <div class="example">
                <p><strong>3. Automation of Tasks:</strong> AI can automate some of the tasks that medical engineers perform, freeing them up to focus on more creative and strategic work.</p>
            </div>
        </section>

        <section id="testYourself" class="content-section">
            <h2>Test Yourself!</h2>
            <form id="quizForm">
                <!-- Questions will be dynamically inserted here by JavaScript -->
            </form>
            <button id="checkAnswersBtn" onclick="checkAnswers()">Check Answers</button>
            <div id="quizResults"></div>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 AI in Medical Engineering Learning App</p>
    </footer>

    <script>
        // Function to display a section and highlight the active tab
        function showSection(sectionId, clickedButton) {
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(button => {
                button.classList.remove('active');
            });
            if (clickedButton) {
                clickedButton.classList.add('active');
            }
        }

        // Quiz Data
        const quizQuestions = [
            {
                id: "q1",
                question: "What does AI stand for?",
                options: {
                    a: "Artificial Intelligence",
                    b: "Automated Information",
                    c: "Advanced Interface"
                },
                correctAnswer: "a"
            },
            {
                id: "q2",
                question: "Which of the following is NOT a task that AI can perform?",
                options: {
                    a: "Problem-solving",
                    b: "Learning",
                    c: "Creative writing",
                    d: "Decision-making"
                },
                correctAnswer: "c"
            },
            {
                id: "q3",
                question: "Machine Learning (ML) is a subset of:",
                options: {
                    a: "Deep Learning (DL)",
                    b: "Artificial Intelligence (AI)",
                    c: "Medical Engineering"
                },
                correctAnswer: "b"
            },
            {
                id: "q4",
                question: "How can AI assist medical engineers in their work?",
                options: {
                    a: "By designing new coffee machines for the lab.",
                    b: "By analyzing medical data to predict potential problems with equipment.",
                    c: "By performing surgeries independently without any human intervention."
                },
                correctAnswer: "b"
            },
            {
                id: "q5",
                question: "Deep Learning often uses structures called 'neural networks' which are inspired by:",
                options: {
                    a: "The structure of crystals",
                    b: "The human brain",
                    c: "The flow of rivers"
                },
                correctAnswer: "b"
            }
        ];

        // Function to load quiz questions into the form
        function loadQuiz() {
            const quizForm = document.getElementById('quizForm');
            if (!quizForm) return; // Exit if form not found
            quizForm.innerHTML = ''; // Clear previous questions

            quizQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.classList.add('quiz-question');
                questionDiv.innerHTML = `<p><strong>${index + 1}. ${q.question}</strong></p>`;
                
                const optionsDiv = document.createElement('div');
                optionsDiv.classList.add('options');

                for (const key in q.options) {
                    const label = document.createElement('label');
                    label.htmlFor = `${q.id}_${key}`;
                    
                    const radio = document.createElement('input');
                    radio.type = 'radio';
                    radio.name = q.id;
                    radio.value = key;
                    radio.id = `${q.id}_${key}`;
                    
                    label.appendChild(radio);
                    label.appendChild(document.createTextNode(` ${q.options[key]}`));
                    optionsDiv.appendChild(label);
                }
                questionDiv.appendChild(optionsDiv);
                quizForm.appendChild(questionDiv);
            });
        }

        // Function to check answers
        function checkAnswers() {
            let score = 0;
            const quizResultsDiv = document.getElementById('quizResults');
            if (!quizResultsDiv) return;
            quizResultsDiv.innerHTML = ''; // Clear previous results

            const quizForm = document.getElementById('quizForm');

            quizQuestions.forEach((q, index) => {
                const selectedOptionInput = quizForm.querySelector(`input[name="${q.id}"]:checked`);
                const questionElements = quizForm.querySelectorAll('.quiz-question');
                const questionElement = questionElements.length > index ? questionElements[index] : null;
                
                // Clear previous styling from labels within this question
                if (questionElement) {
                    const optionLabels = questionElement.querySelectorAll('.options label');
                    optionLabels.forEach(label => {
                        label.style.color = 'inherit';
                        label.style.fontWeight = 'normal';
                        label.style.backgroundColor = 'transparent'; // Reset background
                        label.style.borderColor = '#eee'; // Reset border
                    });
                }

                const resultP = document.createElement('p');
                
                if (selectedOptionInput) {
                    const userAnswer = selectedOptionInput.value;
                    const correctAnswer = q.correctAnswer;
                    const selectedLabel = selectedOptionInput.parentElement;

                    if (userAnswer === correctAnswer) {
                        score++;
                        resultP.innerHTML = `<strong>Question ${index + 1}:</strong> Correct!`;
                        resultP.className = 'correct';
                        if (selectedLabel) {
                           selectedLabel.style.borderColor = '#28a745'; // Green border for correct
                           selectedLabel.style.backgroundColor = '#d4edda';
                        }
                    } else {
                        resultP.innerHTML = `<strong>Question ${index + 1}:</strong> Incorrect. Your answer: "${q.options[userAnswer]}". Correct answer: "${q.options[correctAnswer]}".`;
                        resultP.className = 'incorrect';
                        if (selectedLabel) {
                            selectedLabel.style.borderColor = '#dc3545'; // Red border for incorrect
                            selectedLabel.style.backgroundColor = '#f8d7da';
                        }
                        // Highlight the correct answer's label
                        const correctLabel = quizForm.querySelector(`label[for="${q.id}_${correctAnswer}"]`);
                        if (correctLabel) {
                            correctLabel.style.borderColor = '#28a745';
                            correctLabel.style.backgroundColor = '#d4edda';
                            correctLabel.style.fontWeight = 'bold';
                        }
                    }
                } else {
                    resultP.innerHTML = `<strong>Question ${index + 1}:</strong> Not answered. Correct answer: "${q.options[q.correctAnswer]}".`;
                    resultP.className = 'unanswered';
                     // Highlight the correct answer's label
                    const correctLabel = quizForm.querySelector(`label[for="${q.id}_${q.correctAnswer}"]`);
                    if (correctLabel) {
                        correctLabel.style.borderColor = '#ffc107';
                        correctLabel.style.backgroundColor = '#fff3cd';
                        correctLabel.style.fontWeight = 'bold';
                    }
                }
                quizResultsDiv.appendChild(resultP);
            });

            const finalScoreP = document.createElement('p');
            finalScoreP.innerHTML = `Your final score: <strong>${score}</strong> out of <strong>${quizQuestions.length}</strong>`;
            finalScoreP.className = 'final-score';
            quizResultsDiv.insertBefore(finalScoreP, quizResultsDiv.firstChild); // Prepend score
        }

        // Initial setup when DOM is fully loaded
        document.addEventListener('DOMContentLoaded', () => {
            const firstTabButton = document.querySelector('#tabs .tab-button'); // Assumes first tab is "What is AI?"
            if (firstTabButton) {
                showSection('whatIsAI', firstTabButton);
            }
            loadQuiz();
        });
    </script>
</body>
</html>
